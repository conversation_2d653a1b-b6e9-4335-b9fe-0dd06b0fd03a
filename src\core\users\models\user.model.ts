import {
  Column,
  <PERSON>,
  Table,
  PrimaryKey,
  DataType,
  CreatedAt,
  UpdatedAt,
  HasOne,
  HasMany,
} from 'sequelize-typescript';

@Table({ tableName: 'users', schema: 'public' })
export class User extends Model<User> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4, // Automatically generate UUID
    unique: 'users_id_key',
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  last_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone_prefix: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  birthday: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '0. ch<PERSON><PERSON> chọn, 1. Nam, 2. Nữ',
  })
  gender: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
    comment: 'true là on, false là off thông báo',
  })
  off: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  identity_card: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  identity_type: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '1. Chưa kích hoạt, 2. Đã kích hoạt, 3. Đã khoá',
  })
  status: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Mã thành viên',
  })
  code: string | null;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Là admin hay không?',
  })
  is_admin: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  zipcode: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  id_country: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  id_province: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'user',
    comment: 'user, supper_admin, admin_child',
  })
  role: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  company: string | null;
}
