import { Bucket } from '../utils/buckets';

export abstract class StoragesService {
  abstract download(bucket: Bucket, objectName: string): Promise<Buffer>;
  abstract upload(
    bucketName: Bucket,
    objectName: string,
    buffer: Buffer,
    contentType?: string,
  ): Promise<void>;
  abstract delete(bucket: Bucket, objectName: string): Promise<void>;
  abstract deletePath(bucket: Bucket, objectName: string): Promise<void>;
  abstract exists(bucket: Bucket, objectName: string): Promise<boolean>;
  abstract getSignedUrl(
    bucket: Bucket,
    objectName: string,
    contentType?: string,
  ): Promise<string> | string;
  abstract getUploadSignedUrl(
    bucket: Bucket,
    objectName: string,
    contentType?: string,
  ): Promise<string> | string;
}
