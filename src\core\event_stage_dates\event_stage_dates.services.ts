import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { EventStageDate } from './models/event_stage_date.model';

@Injectable()
export class EventStageDatesService {
  constructor(
    @InjectModel(EventStageDate)
    private readonly eventStageDateModel: typeof EventStageDate,
    private sequelize: Sequelize,
  ) {}

  async getEventStageDateDetail(event_date_id) {
    return await this.eventStageDateModel.findOne({
      where: {
        active: true,
        deleted: false,
        id: event_date_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- <PERSON> is here
    });
  }

  async getListEventStageDate(event_stage_id) {
    return await this.eventStageDateModel.findAll({
      where: {
        active: true,
        deleted: false,
        event_stage_id: event_stage_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- <PERSON> is here
    });
  }
}
