{"name": "vtix-nft", "version": "1.0.0", "description": "Vtix NFT repository", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint '{src,apps,libs,test}/**/*.ts' --fix"}, "dependencies": {"@metaplex-foundation/digital-asset-standard-api": "^1.0.6", "@metaplex-foundation/js": "^0.19.5", "@metaplex-foundation/mpl-bubblegum": "^4.0.0", "@metaplex-foundation/mpl-core": "^0.4.7", "@metaplex-foundation/mpl-token-metadata": "^3.2.1", "@metaplex-foundation/umi": "^0.8.9", "@metaplex-foundation/umi-bundle-defaults": "^0.8.9", "@nestjs/axios": "^3.0.3", "@nestjs/common": "10.3.2", "@nestjs/config": "^3.2.3", "@nestjs/core": "10.3.2", "@nestjs/platform-express": "10.3.8", "@nestjs/schedule": "^4.1.1", "@nestjs/sequelize": "10.0.0", "@solana/spl-account-compression": "^0.2.1", "@solana/spl-token": "^0.3.8", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-wallets": "^0.19.23", "@solana/web3.js": "^1.87.3", "aws-sdk": "^2.1692.0", "axios": "^1.7.7", "bull": "^4.16.5", "ejs": "^3.1.10", "html-pdf": "^3.0.1", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "nest-commander": "^3.15.0", "nodemailer": "^6.9.15", "pdfmake": "^0.2.14", "pg": "^8.13.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.13", "rimraf": "5.0.5", "rxjs": "7.8.1", "sequelize": "6.36.0", "sequelize-typescript": "2.1.6", "telegraf": "^4.16.3", "typescript": "5.3.3"}, "devDependencies": {"@nestjs/cli": "10.3.2", "@nestjs/schematics": "10.1.1", "@nestjs/testing": "10.3.2", "@types/express": "4.17.21", "@types/jest": "29.5.12", "@types/node": "20.8.7", "@types/supertest": "2.0.16", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "eslint": "8.42.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "jest": "29.7.0", "prettier": "3.2.5", "supertest": "6.3.3", "ts-jest": "29.1.2", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}