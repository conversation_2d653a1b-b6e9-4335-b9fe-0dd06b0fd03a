import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

@Command({
  name: 'process-update-cnft-json',
  description: 'Update cnft json',
})
@Injectable()
export class UpdateCnftJsonCommand extends CommandRunner {
  private readonly logger = new Logger(UpdateCnftJsonCommand.name);
  private batchSize = 10; // Default batch size
  private timeDelayCallTransaction = 3 * 1000; // 3 seconds
  private timeDelayNextZone = 0.5 * 60 * 1000; // 30 seconds
  private isProcessingComplete = false;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Thứ tự chạy:
  // B1: Sẽ lấy tất cả vé trong bảng cnft_temp, limit mỗi lần chạy là 10 và deplay 5 phút
  // VD: npx ts-node src/cli.ts update-cnft-json --event_id="a42f0c9c-7dbd-4f58-ad14-adb78c98168f" --event_stage_id=8 --event_date_id=18
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const limit = options?.batchSize || this.batchSize;
    const event_id = options?.event_id;
    const event_stage_id = options?.event_stage_id;
    const event_date_id = options?.event_date_id;
    const event_zone_detail_id = options?.event_zone_detail_id;

    console.info({
      limit,
      event_id,
      event_stage_id,
      event_date_id,
      event_zone_detail_id,
    });

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    const getEventZoneIds = await firstValueFrom(
      this.httpService.get(
        `${app_domain}/_api/events/get-list-event-date-by-zone?event_date_id=${event_date_id}`,
        { headers },
      ),
    );
    const listEventZoneIds = getEventZoneIds.data.data || [];
    if (listEventZoneIds.length === 0) {
      console.info('No event zone found');
      return;
    }

    for (const eventZoneId of listEventZoneIds) {
      const payload = {
        event_id,
        event_stage_id,
        event_date_id,
        event_zone_detail_id: eventZoneId,
      };

      const getTotalCNFTTemp = await firstValueFrom(
        this.httpService.post(
          `${app_domain}/_api/cnft/total-cnft-temp`,
          payload,
          { headers },
        ),
      );
      const totalCNFTTemp =
        getTotalCNFTTemp.data.data.total ?? 0;
      if (totalCNFTTemp === 0) {
        console.info('No cnft temp');
        continue;
      }
      const callNumber = Math.ceil(totalCNFTTemp / limit);
      console.info(
        `Starting update cnft json with Zone: ${eventZoneId} - total: ${totalCNFTTemp} - call: ${callNumber} times`,
      );
      for (let i = 1; i <= callNumber; i++) {
        console.info(
          `----Call ${i} time with ${JSON.stringify({ ...payload, limit: limit })}`,
        );
         await firstValueFrom(
          this.httpService.post(
            `${app_domain}/_api/cnft/update-cnft-json-by-zone`,
            { ...payload, limit: limit },
            { headers },
          ),
        );
        console.info('--------Update cnft json success');
        await this.delayTime(this.timeDelayCallTransaction);
      }
      await this.delayTime(this.timeDelayNextZone);
    }
    console.info('<<<<<<<<<<<All cnft json have been updated.>>>>>>>>>>>>>');
  }

  @Option({
    flags: '-b, --batchSize <batchSize>',
    description: 'Batch size for processing',
  })
  parseBatchSize(val: string): number {
    return parseInt(val, this.batchSize);
  }

  @Option({
    flags: '-event_id, --event_id <event_id>',
  })
  parseEventId(val: string): string {
    return val;
  }

  @Option({
    flags: '-event_stage_id, --event_stage_id <event_stage_id>',
  })
  parseEventStageId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_date_id, --event_date_id <event_date_id>',
  })
  parseEventDateId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_zone_detail_id, --event_zone_detail_id <event_date_id>',
  })
  parseEventZoneDetailId(val: string): number {
    return parseInt(val);
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }
}
