import { Body, Controller, Get, Post, Query, Req, Res } from '@nestjs/common';
import { PublicKey } from '@solana/web3.js';
import { SolanaService } from './solana.services';
import ActivityNft from 'common/activity_nft';
import RestAPI from 'common/rest_api';

@Controller('web3')
export class Web3Controller {
  constructor(private readonly solanaService: SolanaService) {}

  @Get('create-account')
  async createAccount() {
    const account = await this.solanaService.createAccount();
    return {
      publicKey: account.publicKey.toBase58(),
      secretKey: account.secretKey.toString(),
    };
  }

  @Get('airdrop')
  async airdrop(
    @Query('publicKey') publicKey: string,
    @Query('amount') amount: string,
  ) {
    const pk = new PublicKey(publicKey);
    const signature = await this.solanaService.airdropSol(
      pk,
      parseFloat(amount),
    );
    return { signature };
  }

  @Get('balance')
  async getBalance(@Query('publicKey') publicKey: string) {
    const pk = new PublicKey(publicKey);
    const balance = await this.solanaService.getBalance(pk);
    return { balance };
  }

  @Get('/create-merkletree')
  async createMerkletree(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<any> {
    const merkleTree = await ActivityNft.buildMerkleTree(req, '');
    return RestAPI.success(res, merkleTree);
  }
}
