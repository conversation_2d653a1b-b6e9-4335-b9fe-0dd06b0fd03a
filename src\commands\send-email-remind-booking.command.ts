import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import config from '../config/setting';
import * as path from 'path';
import * as ejs from 'ejs';
import nodemailer from 'nodemailer';
import { UsersService } from '@users/users.services';
import { firstValueFrom } from 'rxjs';
import _ from 'lodash';

@Command({
  name: 'process-send-email-remind-booking',
  description:
    'Process send email remind booking for customer had paid not yet with customize templated email',
})
/**
 * Gửi email marketing
 * chỉ định chính xác template cần gửi tại thư mục views/mail/remind_booking/...
 */
@Injectable()
export class SendEmailRemindBookingCommand extends CommandRunner {
  private readonly logger = new Logger(SendEmailRemindBookingCommand.name);
  private timeDelayNextZone = 30 * 1000; // 30 seconds

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Guide:
  // VD: npx ts-node src/cli.ts process-send-email-remind-booking --template="remind_booking" --event_id="f3ff7435-172f-48d0-9fa0-88afd7e0a66f" --approve=true
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const template = options?.template;
    const approve = options?.approve;
    const event_id = options?.event_id;
    const limit = options?.limit;
    console.info({
      message: 'Call email template: ',
      template,
      approve,
      event_id,
    });
    if (!event_id || !template) {
      console.info(
        `<<<<<<<<<<< Params Fail: Missing event_id / template >>>>>>>>>>>>>`,
      );
    }
    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    const limitQuery = limit ? `&limit=${limit}` : ``;
    const getAllPaymentDetailWithEventIDNotPaid = await firstValueFrom(
      this.httpService.get(
        `${app_domain}/_api/payments/remind-booking?event_id=${event_id}${limitQuery}`,
        {
          headers,
        },
      ),
    )
      .then((data) => {
        return _.get(data.data, 'data', []);
      })
      .catch((error) => {
        console.log('error', error);
      });

    //
    // Bắt đầu gửi email
    console.info(
      `<<<<<<<<<<<Total email will send: ${getAllPaymentDetailWithEventIDNotPaid.length}>>>>>>>>>>>>>`,
    );
    let countSentSuccess = 0;
    let countSentFail = 0;

    if (
      getAllPaymentDetailWithEventIDNotPaid &&
      !_.isEmpty(getAllPaymentDetailWithEventIDNotPaid)
    ) {
      for (let i = 0; i < getAllPaymentDetailWithEventIDNotPaid.length; i++) {
        const item = getAllPaymentDetailWithEventIDNotPaid[i];
        const email =
          approve === 'true' ? item.email_booking : '<EMAIL>'; // '<EMAIL>';

        console.info(
          `<<<<<<<<<<<Begin send to ${item.email_booking} | STT: ${i + 1} >>>>>>>>>>>>>`,
        );
        await this.sendMail({
          data: item,
          template: `mail/remind_booking/${template}`,
          email: email,
          subjectEmail: `${item.user_name_booking} ơi, bạn đã đến rất gần [${item.name}] rồi!`,
        })
          .then(async () => {
            countSentSuccess++;
            // Dừng lại sau khi gửi mail thành công
            console.info(`<<<<<<<<<<<Sent ${email} finished>>>>>>>>>>>>>`);
            await this.delayTime(this.timeDelayNextZone);
          })
          .catch((error) => {
            countSentFail++;
          });

        // Chỉ dành cho testing
        // approve = false hoặc undefined
        if (!approve) {
          break;
        }
      }
    }

    console.info(
      `<<<<<<<<<<< RESULT: Sent success: ${countSentSuccess} | Sent fail: ${countSentFail} >>>>>>>>>>>>>`,
    );
  }

  @Option({
    flags: '-template, --template <template>',
  })
  parseTemplate(val: string): string {
    return val;
  }

  @Option({
    flags: '-approve, --approve <approve>',
  })
  parseApprove(val: string): string {
    return val;
  }

  @Option({
    flags: '-event_id, --event_id <event_id>',
  })
  parseEventId(val: string): string {
    return val;
  }

  @Option({
    flags: '-limit, --limit <limit>',
  })
  parseLimit(val: number): number {
    return val;
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }

  // Func gửi email
  async sendMail({ data, template, email, subjectEmail }) {
    try {
      const transporter = nodemailer.createTransport({
        service: config.config_email.service,
        port: config.config_email.port,
        host: config.config_email.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email.email_user,
          pass: config.config_email.email_password,
        },
      });
      const templatePath = path.join(
        __dirname,
        '..',
        'views',
        `${template}.ejs`,
      );

      return await ejs.renderFile(
        templatePath,
        data,
        async function (err, html) {
          if (err) {
            console.log('Error rendering template:', err);
            return;
          }
          const mailOptions = {
            from: config.config_email.email_sender,
            to: email,
            subject: `[VTIX] ${subjectEmail}`,
            html: html,
          };
          return await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
            } else {
              console.log('SEND MAIL SUCCESS TO', email);
            }
          });
        },
      );
    } catch (error) {
      console.log('Send email marketing error > ', error);
    }
  }
}
