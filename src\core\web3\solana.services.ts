import { Injectable } from '@nestjs/common';
import {
  Connection,
  PublicKey,
  Keypair,
  clusterApiUrl,
  Transaction,
  LAMPORTS_PER_SOL,
} from '@solana/web3.js';
import {
  createMint,
  getOrCreateAssociatedTokenAccount,
  mintTo,
  burnChecked,
} from '@solana/spl-token';

@Injectable()
export class SolanaService {
  private connection: Connection;
  private payer: Keypair;

  constructor() {
    // Connect to the Solana Devnet
    this.connection = new Connection(clusterApiUrl('devnet'), 'confirmed');
    // Generate a new keypair for the payer
    this.payer = Keypair.generate();
  }

  async createAccount(): Promise<Keypair> {
    const keypair = Keypair.generate();
    console.log('Account created:', keypair.publicKey.toBase58());
    return keypair;
  }

  // Airdrop SOL to an account
  async airdropSol(publicKey: PublicKey, amount: number): Promise<string> {
    const signature = await this.connection.requestAirdrop(
      publicKey,
      amount * LAMPORTS_PER_SOL,
    );

    // Updated method for confirming transactions
    const latestBlockhash = await this.connection.getLatestBlockhash();
    await this.connection.confirmTransaction({
      signature,
      blockhash: latestBlockhash.blockhash,
      lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
    });

    console.log(`Airdropped ${amount} SOL to ${publicKey.toBase58()}`);
    return signature;
  }

  // Get account balance
  async getBalance(publicKey: PublicKey): Promise<number> {
    const balance = await this.connection.getBalance(publicKey);
    console.log(
      `Balance of ${publicKey.toBase58()}: ${balance / LAMPORTS_PER_SOL} SOL`,
    );
    return balance / LAMPORTS_PER_SOL;
  }

  // Mint a new NFT
  async mintNFT(): Promise<{
    mintAddress: string;
    tokenAccountAddress: string;
  }> {
    // Create a new mint
    const mint = await createMint(
      this.connection,
      this.payer,
      this.payer.publicKey,
      null,
      0, // Decimals set to 0 for NFT
    );

    // Create a token account for the mint
    const tokenAccount = await getOrCreateAssociatedTokenAccount(
      this.connection,
      this.payer,
      mint,
      this.payer.publicKey,
    );

    // Mint 1 token (NFT) to the token account
    await mintTo(
      this.connection,
      this.payer,
      mint,
      tokenAccount.address,
      this.payer.publicKey,
      1,
    );

    return {
      mintAddress: mint.toBase58(),
      tokenAccountAddress: tokenAccount.address.toBase58(),
    };
  }
}
