import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import _ from 'lodash';
import { User } from './models/user.model';
import { Op } from 'sequelize';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}
  async getAllUserRole(params: any) {
    const { role, limit } = params;

    const innerQuery: any = {
      where: {
        active: true,
        deleted: false,
        role: 'user',
        [Op.and]: [
          //   {
          //     [Op.or]: [
          //       { email: { [Op.iLike]: `%gmail.com%` } },
          //       { email: { [Op.iLike]: `%yahoo.com%` } },
          //     ],
          //   },
          {
            email: { [Op.notILike]: `%strongtie.com%` },
          },
          //   {
          //     email: { [Op.notILike]: `%gmail.com%` },
          //   },
          {
            [Op.or]: [{ company: null }, { company: '' }],
          },
        ],
      },
      attributes: ['name', 'last_name', 'phone', 'email'],
      order: [['name', 'ASC']],
      raw: true, // <----------- <PERSON> is here
      nest: true, // <----------- Magic is here
    };

    if (limit && limit !== undefined) {
      _.assign(innerQuery, { limit: parseInt(limit) });
    }

    const list: any = await this.userModel.findAll(innerQuery);
    return list;
  }
}
