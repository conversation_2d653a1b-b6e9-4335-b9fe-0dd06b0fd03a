import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { EventSeatPricesService } from './event_seat_prices.services';
import { EventSeatPrice } from './models/event_seat_price.model';

@Module({
  imports: [SequelizeModule.forFeature([EventSeatPrice])],
  providers: [EventSeatPricesService],
  exports: [EventSeatPricesService],
})
export class EventSeatPricesModule {}
