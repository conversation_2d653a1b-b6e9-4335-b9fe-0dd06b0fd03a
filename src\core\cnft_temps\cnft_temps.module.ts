import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CNftTempsService } from './cnft_temps.services';
import { CNftTemp } from './models/cnft_temp.model';
import { CNftController } from './cnft_temps.controller';
import { EventZoneDetailsModule } from '@event_zone_details/event_zone_details.module';
import { EventSeatPricesModule } from '@event_seat_prices/event_seat_prices.module';
import { EventsModule } from '@events/events.module';
import { EventStageDatesModule } from '@event_stage_dates/event_stage_dates.module';
import { StoragesModule } from 'storages/storages.module';

@Module({
  imports: [
    SequelizeModule.forFeature([CNftTemp]),
    EventZoneDetailsModule,
    EventSeatPricesModule,
    EventsModule,
    EventStageDatesModule,
    StoragesModule,
  ],
  providers: [CNftTempsService],
  controllers: [CNftController],
  exports: [CNftTempsService],
})
export class CNftTempsModule {}
