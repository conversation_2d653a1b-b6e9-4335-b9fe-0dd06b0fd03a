import { Module } from '@nestjs/common';
import { CnftTransactionCommand } from './cnft-transaction.command';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { CnftMintCommand } from './cnft-mint.command';
import { GenerateTicketQrMintAddressCommand } from './generate-ticket-qr-mint-address.command';
import { GenerateTicketQrCommand } from './generate-ticket-qr.command';
import { SendEmailMarketingCommand } from './send-email-marketing.command';
import { SendPinEventMarketingCommand } from './send-pin-event-marketing.command';
import { SendPinSportMarketingCommand } from './send-pin-sport-marketing.command';
import { SendEmailRemindBookingCommand } from './send-email-remind-booking.command';
import { SendPinSingleSportMarketingCommand } from './send-pin-single-sport-marketing.command';
import { UpdateCnftJsonCommand } from './update-cnft-json.command';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    HttpModule,
  ],
  providers: [
    CnftTransactionCommand,
    CnftMintCommand,
    GenerateTicketQrMintAddressCommand,
    GenerateTicketQrCommand,
    SendEmailMarketingCommand,
    SendPinEventMarketingCommand,
    SendPinSportMarketingCommand,
    SendEmailRemindBookingCommand,
    SendPinSingleSportMarketingCommand,
    UpdateCnftJsonCommand,
  ],
})
export class CliModule {}
