import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as process from 'process';
import * as path from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { cors: true });
  app.setGlobalPrefix('_api');
  // Set up the express instance
  const expressApp = app.getHttpAdapter().getInstance();

  // Set the views directory
  expressApp.set('views', path.join(__dirname, '..', 'views'));

  // Set the view engine to ejs
  expressApp.set('view engine', 'ejs');

  await app.listen(process.env.APP_PORT || 4001);
  console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
