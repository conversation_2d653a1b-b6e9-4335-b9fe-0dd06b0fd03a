import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { VoucherProvider } from './models/voucher_provider.model';
import { CreationAttributes } from 'sequelize';
import { Voucher } from 'core/vouchers/models/voucher.model';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class VoucherProviderService {
  constructor(
    @InjectModel(VoucherProvider)
    private readonly voucherProviderModel: typeof VoucherProvider,
    @InjectModel(Voucher)
    private readonly voucherModel: typeof Voucher,

    private sequelize: Sequelize,
  ) { }

  async getListVoucherProvider(conditions: any) {
    const { page = 1, limit = 10, ...rest } = conditions;
    const offset = (page - 1) * limit;
    const [result, total] = await Promise.all([
      this.voucherProviderModel.findAll({
        where: {
          ...rest,
        },
        offset,
        limit,
        raw: true,
      }),
      this.voucherProviderModel.count({
        where: {
          ...rest,
        },
      }),
    ]);

    return {
      list: result,
      total,
      limit: parseInt(limit),
      page: parseInt(page),
    };
  }

  async getVoucherProviderDetail(conditions: any) {
    return await this.voucherProviderModel.findOne({
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async create(
    payload: any,
    isCreateVoucher: boolean = false,
  ) {
    console.log('isCreateVoucher :>> ', isCreateVoucher);
    const voucherProvider = await this.voucherProviderModel.create(
      payload as CreationAttributes<VoucherProvider>,
    );

    if (isCreateVoucher && voucherProvider.total_amount > 0) {
      // Generate vouchers based on total_amount
      const vouchers = Array.from(
        { length: voucherProvider.total_amount },
        async (item, index) =>
          ({
            voucher_provider_id: voucherProvider.id,
            name: `Voucher ${index + 1} from ${voucherProvider.provider_name}`,
            code: await this.generateUniqueVoucherCode(voucherProvider.id),
            photo: `voucher_${index + 1}.jpg`,
            description: `Special voucher from ${voucherProvider.provider_name}`,
            discount_type: index % 2 === 0 ? 'percentage' : 'fixed',
            discount_value: Math.floor(Math.random() * 50) + 10,
            status: true,
          }) as CreationAttributes<Voucher>,
      );
      const vouchersData = await Promise.all(vouchers);
      await this.voucherModel.bulkCreate(vouchersData);
    }

    return voucherProvider;
  }

  private async generateUniqueVoucherCode(
    voucherProviderId: string,
  ): Promise<string> {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const codeLength = 8;
    let maxAttempts = 10;
    let uniqueCode: string;

    while (maxAttempts > 0) {
      // Generate random code
      uniqueCode = Array(codeLength)
        .fill(null)
        .map(() => chars[Math.floor(Math.random() * chars.length)])
        .join('');

      // Check if code exists for this voucher provider
      const existingVoucher = await this.voucherModel.findOne({
        where: {
          voucher_provider_id: voucherProviderId,
          code: uniqueCode,
        },
      });

      if (!existingVoucher) {
        return uniqueCode;
      }

      maxAttempts--;
    }

    throw new Error(
      'Failed to generate unique voucher code after multiple attempts',
    );
  }

  async bulkCreate(dataInsert: any[]) {
    return await this.voucherProviderModel.bulkCreate(dataInsert);
  }

  async removeVoucherProvider(conditions: any): Promise<any> {
    try {
      await this.voucherProviderModel.destroy({
        where: {
          ...conditions,
        },
      });
    } catch (error) {
      console.log('error :>> ', error.message);
    }
  }

  async updateStatusVoucherProvider(id, status) {
    return await this.voucherProviderModel.update(
      { status },
      { where: { id } },
    );
  }
}
