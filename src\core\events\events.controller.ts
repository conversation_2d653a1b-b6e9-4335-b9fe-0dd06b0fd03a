import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Patch,
  UseGuards,
  Req,
  Res,
  Query,
} from '@nestjs/common';
import { EventsService } from './events.services';
import _ from 'lodash';
import { InternalAPIGuard } from 'guards/internalAPI.guard';
import RestAPI from 'common/rest_api';

@Controller('')
export class EventsController {
  constructor(private readonly eventService: EventsService) {}

  @Get('/events/get-list-event-date-by-zone')
  @UseGuards(InternalAPIGuard)
  async getListEventDateByZone(
    @Query()
    params: { event_date_id: number },
    @Res() res: Response,
  ): Promise<any> {
    const listData = await this.eventService.getListEventDateByZone({
      ..._.pick(params, ['event_date_id']),
    });
    return RestAPI.success(res, listData);
  }
}
