import { EventsService } from '@events/events.services';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import config from '../../config/setting';
import moment from 'moment';
import Common from 'common/common';
import Constants from 'common/constants';
import { EventStageDatesService } from '@event_stage_dates/event_stage_dates.services';
import { User } from '@users/models/user.model';
import { Event } from '@events/models/event.model';
import { EventStagesService } from '@event_stages/event_stages.services';
import { EventZoneDetailsService } from '@event_zone_details/event_zone_details.services';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import _ from 'lodash';
import { EventSeatPricesService } from '@event_seat_prices/event_seat_prices.services';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import nodemailer from 'nodemailer';
import moment_timezone from 'moment-timezone';
import fs from 'fs';
import * as path from 'path';
import * as ejs from 'ejs';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Event)
    private readonly eventModel: typeof Event,
    @InjectModel(EventStage)
    private readonly eventStageModel: typeof EventStage,
    @InjectModel(EventStageDate)
    private readonly eventStageDateModel: typeof EventStageDate,
    @InjectModel(EventZoneDetail)
    private readonly eventZoneDetailModel: typeof EventZoneDetail,
    @InjectModel(PaymentDetail)
    private readonly paymentDetailModel: typeof PaymentDetail,

    private eventService: EventsService,
    private eventStageService: EventStagesService,
    private eventStageDateService: EventStageDatesService,
    private eventZoneDetailService: EventZoneDetailsService,
    private eventSeatPricesService: EventSeatPricesService,
  ) {}

  // Lấy toàn bộ những giao dịch chưa thanh toán theo EVENT ID
  // Payload
  // {
  //     "event_id": "52d46052-d852-4fe0-8530-0d3fc6941300",
  //     "limit": number (optional)
  // }
  async getAllPaymentDetailWithEventIDNotPaid(params: any) {
    const { event_id, limit } = params;

    const wherePaymentDetail: any = {
      event_id: event_id,
      //   status_payment: false,
    };

    if (!event_id || _.isEmpty(event_id) || _.isEmpty(event_id)) {
      return [];
    } else {
      const innerQuery: any = {
        where: wherePaymentDetail,
        raw: true,
        nest: true,
      };

      if (limit && limit !== undefined) {
        _.assign(innerQuery, { limit: parseInt(limit) });
      }

      const data = await this.eventModel
        .findOne({
          where: {
            id: event_id,
            active: true,
            deleted: false,
          },
          attributes: [
            'id',
            ['attributes', 'attributes_event'],
            'showdate_from',
            'event_group_id',
            ['name_vi', 'name'],
            ['slug_vi', 'slug'],
            ['venue_vi', 'venue'],
            ['poster_vi', 'poster'],
            ['desc_unlimited_vi', 'desc_unlimited'],
            'is_unlimited',
          ],
          raw: true,
          nest: true,
        })
        .then(async (eventDetail: any) => {
          if (!eventDetail || _.isEmpty(eventDetail)) return [];

          //
          // Bắt đầu tìm payment detail của event
          return await this.paymentDetailModel
            .findAll(innerQuery)
            .then(async (listPaymentDetail: any) => {
              //
              // List danh sách email đã mua và thanh toán rồi
              const listEmailPaidAldready: any = [];
              for (let i = 0; i < listPaymentDetail.length; i++) {
                const paymentDetail = listPaymentDetail[i];
                if (paymentDetail.status_payment) {
                  listEmailPaidAldready.push(paymentDetail.email);
                }
              }
              // Kết thúc danh sách đã mua và thanh toán rồi

              const listDataSendEmail: any = [];
              const listAvoidDuplicateEmail: any = [];
              for (let i = 0; i < listPaymentDetail.length; i++) {
                const paymentDetail = listPaymentDetail[i];

                if (_.includes(listEmailPaidAldready, paymentDetail.email)) {
                  continue;
                }
                if (_.includes(listAvoidDuplicateEmail, paymentDetail.email)) {
                  continue;
                }

                if (
                  _.isEmpty(paymentDetail.email) ||
                  _.isNil(paymentDetail.email) ||
                  _.isEmpty(paymentDetail.user_name) ||
                  _.isNil(paymentDetail.user_name)
                ) {
                  continue;
                }

                listAvoidDuplicateEmail.push(paymentDetail.email);
                //
                // Bắt đầu tìm zone và seat tương ứng
                const event_zone_detail_ids =
                  paymentDetail.event_zone_detail_id.split(',');
                const event_zone_detail_infos: any = [];
                for (const event_zone_detail_id of event_zone_detail_ids) {
                  event_zone_detail_infos.push(
                    await this.eventZoneDetailService.getEventZoneDetail(
                      Number(event_zone_detail_id),
                    ),
                  );
                }
                const zone_names = event_zone_detail_infos.reduce(
                  (acc, item) => {
                    acc[item.id] = item.zone_name;
                    return acc;
                  },
                  {},
                );
                const zone_name = Object.values(zone_names).join(', ');

                const seat_name =
                  eventDetail?.attributes_event?.is_sport_run !== undefined &&
                  eventDetail?.attributes_event?.is_sport_run
                    ? null
                    : paymentDetail.event_obj
                        .map((item) => item.seat_code)
                        .join(', ');
                // Kết thúc tìm zone & seat

                const showdate_from = eventDetail?.is_unlimited
                  ? moment().tz(Constants.DEFAULT_TIMEZONE).format('X')
                  : eventDetail.showdate_from;
                const emailData = {
                  show_from: eventDetail.desc_unlimited
                    ? eventDetail.desc_unlimited
                    : `${_.capitalize(
                        Common.getDateFormat(
                          showdate_from,
                          Constants.DAY_OF_WEEK_VI,
                        ),
                      )} ${eventDetail?.attributes_event?.show_hour == undefined || eventDetail?.attributes_event?.show_hour ? _.toUpper(Common.getDateFormat(showdate_from, 'H:mm')) : ''}`,
                  poster: `https://vtix.vn${Common.renderFullPathImageTicket(
                    eventDetail,
                    eventDetail.poster,
                  )}`,
                  code_order: paymentDetail.code,
                  venue: eventDetail.venue,
                  name: eventDetail.name,
                  discounted_price: paymentDetail.discounted_price,
                  email_booking: paymentDetail.email,
                  phone_booking: paymentDetail.phone,
                  user_name_booking: paymentDetail.user_name,
                  zone_name: zone_name,
                  seat_name: seat_name,
                  link: `https://vtix.vn/vi${Common.renderFullPathImageTicket(
                    eventDetail,
                    eventDetail.slug,
                  )}`,
                };
                listDataSendEmail.push(emailData);
              }

              return listDataSendEmail;
            });
          // Kết thúc tìm payment detail
        });
      // Kết thúc kiểm tra event

      return data;
    }
  }
  // Kết  Lấy toàn bộ những giao dịch chưa thanh toán theo EVENT ID
}
