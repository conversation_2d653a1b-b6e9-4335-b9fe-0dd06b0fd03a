import { registerAs } from '@nestjs/config';

export default registerAs('file', () => {
  return {
    driver: process.env.FILE_DRIVER ?? 'local',
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
    awsS3MediaBucket: process.env.AWS_S3_MEDIA_BUCKET,
    awsDefaultS3Url: process.env.AWS_DEFAULT_S3_URL,
    awsS3Region: process.env.AWS_S3_REGION,
    awsCloudfrontCdnDistributionId:
      process.env.AWS_CLOUDFRONT_CDN_DISTRIBUTION_ID,
    maxFileSize: 5242880, // 5mb
  };
});
