import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Event } from '@events/models/event.model';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventSeatPrice } from '@event_seat_prices/models/event_seat_price.model';

interface Attributes {
  [key: string]: any;
}

@Table({
  tableName: 'event_zone_detail',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class EventZoneDetail extends Model<EventZoneDetail> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @ForeignKey(() => EventStage)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Stage',
  })
  event_stage_id: number;

  @ForeignKey(() => EventStageDate)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event date',
  })
  event_date_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Zonemap vị trí',
  })
  zone_map_position: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Zonemap - tên vị trí',
  })
  zone_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Zonemap - image',
  })
  zone_image: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả giá - VI',
  })
  ticket_price_desc_vi: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
    comment: '1: vé ngồi | 2: standing',
  })
  seat_type: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả giá - EN',
  })
  ticket_price_desc_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Zone - area shape',
  })
  area_shape: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'NFT name - đức vé sẽ thêm # B1 B2',
  })
  nft_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'NFT symbol',
  })
  nft_symbol: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'NFT image',
  })
  nft_image: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '0: không bị reversed | 1: bị reversed',
  })
  is_reversed: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Khi mua đạt số lượng sẽ được tặng vé.',
  })
  buy_required: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Số lượng vé được tặng khi mua đạt mục tiêu.',
  })
  get_free_quantity: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment:
      'Email poster cho từng zone, trường hợp null, pdf vé sẽ lấy email poster từ table event',
  })
  email_poster: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  updated_at: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Color of zone',
  })
  color: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Mô tả quyền lợi của khách hàng khi mua vé trong zone',
  })
  benifits_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Mô tả quyền lợi của khách hàng khi mua vé trong zone',
  })
  benifits_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: '#ffffff',
    comment: 'Màu chữ cho từng zone',
  })
  color_text: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hiển thị tên của zone',
  })
  zone_title: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hiển thị mô tả cho từng zone (nếu có)',
  })
  description_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hiển thị mô tả cho từng zone (nếu có)',
  })
  description_en: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: {},
  })
  attributes: Attributes;

  // Associations
  @BelongsTo(() => EventStageDate, {
    foreignKey: 'event_date_id',
    as: 'event_stage_date',
  })
  event_stage_date: EventStageDate;

  @BelongsTo(() => EventStage, {
    foreignKey: 'event_stage_id',
    as: 'event_stage',
  })
  event_stage: EventStage;

  @HasMany(() => EventSeatPrice, {
    foreignKey: 'zone_detail_id',
    as: 'event_seat_price',
  })
  event_seat_price: EventSeatPrice[];
}
