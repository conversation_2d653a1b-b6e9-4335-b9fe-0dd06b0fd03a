import { Injectable } from '@nestjs/common';
import { CloudFront, Credentials } from 'aws-sdk';
import { CloudFrontService } from './cloudfront.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CloudFrontProvider extends CloudFrontService {
  private cloudFront: CloudFront;

  constructor(private configService: ConfigService) {
    super();
    const awsConfig = {
      credentials: new Credentials({
        accessKeyId: configService.getOrThrow('file.accessKeyId', {
          infer: true,
        }),
        secretAccessKey: configService.getOrThrow('file.secretAccessKey', {
          infer: true,
        }),
      }),
    };
    this.cloudFront = new CloudFront(awsConfig);
  }

  async invalidateCache(
    distributionId: string,
    paths: string[],
  ): Promise<void> {
    if (!distributionId) {
      throw new Error('invalid distributionId');
    }
    const params: CloudFront.Types.CreateInvalidationRequest = {
      DistributionId: distributionId,
      InvalidationBatch: {
        CallerReference: new Date().toISOString(),
        Paths: {
          Quantity: paths.length,
          Items: paths,
        },
      },
    };

    try {
      console.log({ params });
      const result = await this.cloudFront.createInvalidation(params).promise();
      console.log('CloudFront Invalidation Result:', result);
    } catch (error) {
      console.error('Error invalidating CloudFront cache:', error);
      throw error;
    }
  }
}
