import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserHasVoucherService } from './user_has_vouchers.services';
import { UserHasVoucher } from './models/user_has_voucher.model';
import { Voucher } from 'core/vouchers/models/voucher.model';
import { VoucherProvider } from '@voucher_providers/models/voucher_provider.model';
import { User } from '@users/models/user.model';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import { UserBookingLog } from 'core/user_booking_log/models/user_booking_log.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      UserHasVoucher,
      Voucher,
      VoucherProvider,
      User,
      PaymentDetail,
      UserBookingLog,
    ]),
  ],
  providers: [UserHasVoucherService],
  exports: [UserHasVoucherService],
})
export class UserHasVouchersModule { }
