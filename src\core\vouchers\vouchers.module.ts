import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { VoucherService } from './vouchers.services';
import { Voucher } from './models/voucher.model';
import { VoucherProvider } from 'core/voucher_providers/models/voucher_provider.model';
import { StoragesModule } from 'storages/storages.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Voucher, VoucherProvider]),
    StoragesModule,
  ],
  providers: [VoucherService],
  exports: [VoucherService],
})
export class VouchersModule { }
