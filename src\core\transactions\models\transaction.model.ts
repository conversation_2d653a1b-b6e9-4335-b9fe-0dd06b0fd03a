import {
  Column,
  Model,
  Table,
  PrimaryKey,
  DataType,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasOne,
} from 'sequelize-typescript';
import { User } from '@users/models/user.model';
import { Event } from '@events/models/event.model';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';

@Table({
  tableName: 'transactions',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class Transaction extends Model<Transaction> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user cập nhật từ table `users` sau khi được tạo ra.',
  })
  user_id: string;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user parent organizer',
  })
  event_id: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_stage_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_stage_date_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_zone_detail_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  timmer: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  is_invoice: boolean;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  // Associations
  @BelongsTo(() => User, {
    foreignKey: 'user_id',
    as: 'user',
  })
  user: User;

  @HasOne(() => PaymentDetail, {
    foreignKey: 'transaction_id',
    as: 'payment_detail',
    onDelete: 'CASCADE',
  })
  payment_detail: PaymentDetail;
}
