import * as dotenv from 'dotenv';
dotenv.config();

const settings = {
  development: {
    protocol: 'http',
    port: 3456,
    database: {
      username: '',
      password: '',
      database: '',
      host: '',
      dialect: '',
      port: '',
    },
    slash: '/', //if Windows, change this to "\\" & NOT submit to server n
    upload_storage_path: 'file_data/', //MUST be same as declaration of data path in app.js for uploading function
    site_key: 'f90d6859-f5f6', //session secret
    secret: 'f90d6859-f5f6-401f-94ef-40d870f79c8d', //session secret
    pwd_extension: 'Eng',
    timerKeepingSeat: 30 * 60, // 600 second keep seat = 10 minutes
    id_vietnam_country: 240,
    token: {
      secret: 'f90d6859-f5f6-401f-94ef-40d870f79c8d', //session secret
      expiresIn: 30 * 24 * 3600, // 30 ngày
    },
    token_hasura: {
      secret:
        '2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d6009', //secret cấu hình để claim hasura
      expiresIn: 60 * 60 * 24, //thời gian hết hạn jwt hasura 1 ngày / ngày 60*60*24*1
    },
    token_refesh: {
      secret:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmdWxsbmFtZSI6IlRy4bqnbiBNaW5oIFR1eeG6v24iLCJ1c2VySWQiOjE4NCwiYWNjb3VudF9pZCI6MTg2LCJhcHBfaWQiOiJ0dXllbjEyMyIsImRldmljZV9pZCI6InR1eWVuMTIzIiwidGltZV9taSI6MTYyNzI5MTY2NjAwMCwiaWF0IjoxNjI3MjkxNjY2LCJleHAiOjE2Mjk4ODM2NjZ9.vwUSrE4DpGbZw6goRLsaXfTderVdgVZ4ZZVii-B3nw4', //secret cấu hình để claim hasura
      expiresIn: 60 * 60 * 24 * 100, //thời gian hết hạn token refesh 100 ngày || nhưng thường 15 phút hết hạn vì refesh sẽ xóa trong DB
    },
    captchaExpiresIn: 15 * 60,
    config_momo: {
      partner_code: 'MOMOBKUN20180529',
      partner_name: 'Test',
      store_id: 'MOMOBKUN20180529',
      request_type: 'captureWallet',
      ipn_url: '/_api/payment/momo_ipn', //link momo gọi về khi thành công
      secret_key: 'at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa',
      access_key: 'klm05TvNBzhg7h7j',
      base_url: 'https://test-payment.momo.vn', //domain momo
      url_create_momo: '/v2/gateway/api/create',
    },
    config_shopee_pay: {
      merchant_ext_id: 'Vtix',
      store_ext_id: 'Vtix',
      client_id: '12000665',
      secret_key: 'jnGsl9QRejgy3uzPFzjtRaqSMl4iBtsH',
      base_url: 'https://api.uat.wallet.airpay.vn',
      ShopDomain: 'http://localhost:3000',
    },
    config_payoo: {
      BusinessUsername: 'SB_VTIX',
      ShopID: '11908',
      ShopTitle: 'VTIX',
      ShopDomain: 'http://localhost:3000',
      ChecksumKey: 'N2Q2ZmZjODM4YmQ4YTBkNDJjNzg5N2I0ZGE1NzMxOGY=',
      APIUsername: 'SB_VTIX_BizAPI',
      APIPassword: 'gfZ35wguKn50uuLS',
      APISignature: '/kOWNOwoB4jEKHa/fjYsAa0Emm1i/WIqdXZdaTy9CDw=',
      base_Url: 'https://newsandbox.payoo.com.vn/v2',
      backend_base_url: 'https://biz-sb.payoo.vn/BusinessRestAPI.svc',
      url_create_payoo: '/create-preorder',
      ipn_url: '/_api/payment/payoo_ipn', //link payoo gọi về khi thành công
      payment_group_bank: 'bank-account',
      payment_group_credit: 'cc',
    },
    config_vietqr: {
      APICallback: 'http://localhost:3000',
      APIUsername: 'vietqr',
      APIPassword: '12345@789',
      vietqr_username: 'customer-vso16814wsvtix-user2471',
      vietqr_password: 'Y3VzdG9tZXItdnNvMTY4MTR3c3Z0aXgtdXNlcjI0NzE=',
      base_url: 'https://api.vietqr.org/vqr',
      url_get_token: '/api/token_generate',
      url_test_transaction: '/bank/api/test/transaction-callback',
      url_generate_customer: '/api/qr/generate-customer',
      url_check_order: '/api/transactions/check-order',
      bankAccount: '************',
      userBankName: 'CÔNG TY CỔ PHẦN VTIX MEDIA ENTERTAINMENT',
      bankCode: 'MB',
      uuid_vietqr: 'f9bb2282-aec8-4e45-9600-f04aa823bfe7',
      secret:
        '2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d1003', //secret cấu hình để claim hasura
      expiresIn: 60, //thời gian hết hạn jwt hasura 1 ngày / ngày 60*60*24*1
    },
    config_nft: {
      nft_mainnet: 'https://api.devnet.solana.com',
      nft_address: 'B4Ab1GHdas92yDPetwdNXJmZVdzbzUpFx7EoySFL5XPA',
      nft_pubkey: '7DHrb21BMipYNpRg2SZYgaNpHMtkCfogKAShuaAuqQpc',
      nft_keypair:
        '[211,136,202,205,117,219,220,29,215,106,9,255,184,73,246,150,44,253,243,139,232,161,226,170,104,63,92,148,249,85,232,174,92,76,38,50,129,47,154,27,136,57,151,148,146,160,248,79,34,81,114,169,140,99,235,206,152,198,118,176,220,116,144,165]',
      sellerFeeBasisPoints: 100, // phi ban quyen 1%
      nft_symbol_default: 'VTX',
      nft_name_default: 'VTX TICKET',
      nft_image_Default:
        'https://lh3.googleusercontent.com/pw/ADCreHcoLlfpwjKFZGkHHOb9JbFO5zYRJm1-2zVmPDNAcZ8fRBTJQo8FAnloni9x7fOnAVxPYuEoZ-di2aNfRn7V2qvkZaCyTzSVwFJljtp2pY6454ltcgchFliZezrS_qFfq0rCqu8S9LNsuP8e320jBjXu=w1344-h1900-s-no',
      RPC_URL: 'https://api.devnet.solana.com',
      merkle_tree_pubkey: 'J8ifcBKknvNVwWtdm4X7yNagwRsUhXjGytuxg1FqWVCW', // tree that
      merkle_tree_keypair:
        '[95,73,82,117,101,71,152,227,62,124,50,57,238,19,110,102,36,183,235,73,250,204,183,12,213,151,201,121,16,236,188,29,254,144,218,218,3,35,196,7,147,182,249,137,149,158,153,8,137,96,246,188,205,198,93,180,204,8,93,128,60,119,159,83]',
    },
    config_email: {
      service: 'smtp',
      host: 'mail.vtix.vn',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      email_user: '<EMAIL>',
      email_password: 'P@ssword!@#123',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_email_marketing: {
      service: 'smtp',
      host: 'mail.vtix.vn',
      port: 465,
      email_sender: 'VTIX NEWS <<EMAIL>>',
      email_user: '<EMAIL>',
      email_password: 'mR2,YF03NfufTcM',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_email_test: {
      service: 'smtp',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      email_user: 'AKIAQ2KT25JEMPW6C6OB',
      email_password: 'BA3Gu0x6FVkDwIp6endSN2m3A6MfFkTiPJYtJ0uVOgW4',
      base_url: 'https://vtix.vn',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
  },
  staging: {
    protocol: 'https',
    port: 3030,
    database: {
      username: '',
      password: '',
      database: '',
      host: '',
      dialect: 'postgres',
      dialectOptions: {
        ssl: {
          require: true,
          rejectUnauthorized: false,
        },
      },
      port: '5432',
    },
    slash: '/', //if Windows, change this to "\\" & NOT submit to server n
    upload_storage_path: 'file_data/', //MUST be same as declaration of data path in app.js for uploading function
    site_key: 'f90d6859-f5f6', //session secret
    secret: 'f90d6859-f5f6-401f-94ef-40d870f79c8d', //session secret
    pwd_extension: 'Eng',
    timerKeepingSeat: 900, // 600 second keep seat
    id_vietnam_country: 240,
    token: {
      secret: 'f90d6859-f5f6-401f-94ef-40d870f79c8d', //session secret
      expiresIn: 30 * 24 * 3600, //seconds
    },
    token_hasura: {
      secret:
        '2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d6009', //secret cấu hình để claim hasura
      expiresIn: 60 * 60 * 24, //thời gian hết hạn jwt hasura 1 ngày / ngày 60*60*24*1
    },
    token_refesh: {
      secret:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmdWxsbmFtZSI6IlRy4bqnbiBNaW5oIFR1eeG6v24iLCJ1c2VySWQiOjE4NCwiYWNjb3VudF9pZCI6MTg2LCJhcHBfaWQiOiJ0dXllbjEyMyIsImRldmljZV9pZCI6InR1eWVuMTIzIiwidGltZV9taSI6MTYyNzI5MTY2NjAwMCwiaWF0IjoxNjI3MjkxNjY2LCJleHAiOjE2Mjk4ODM2NjZ9.vwUSrE4DpGbZw6goRLsaXfTderVdgVZ4ZZVii-B3nw4', //secret cấu hình để claim hasura
      expiresIn: 60 * 60 * 24 * 100, //thời gian hết hạn token refesh 100 ngày || nhưng thường 15 phút hết hạn vì refesh sẽ xóa trong DB
    },
    captchaExpiresIn: 15 * 60,
    config_momo: {
      partner_code: 'MOMOBKUN20180529',
      partner_name: 'Test',
      store_id: 'MOMOBKUN20180529',
      request_type: 'captureWallet',
      ipn_url: '/_api/payment/momo_ipn', //link momo gọi về khi thành công
      secret_key: 'at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa',
      access_key: 'klm05TvNBzhg7h7j',
      base_url: 'https://test-payment.momo.vn', //domain momo
      url_create_momo: '/v2/gateway/api/create',
    },
    config_shopee_pay: {
      merchant_ext_id: 'Vtix',
      store_ext_id: 'Vtix',
      client_id: '12000665',
      secret_key: 'jnGsl9QRejgy3uzPFzjtRaqSMl4iBtsH',
      base_url: 'https://api.uat.wallet.airpay.vn',
      ShopDomain: 'https://staging.vtix.vn',
    },
    config_payoo: {
      BusinessUsername: 'SB_VTIX',
      ShopID: '11908',
      ShopTitle: 'VTIX',
      ShopDomain: 'https://staging.vtix.vn',
      ChecksumKey: 'N2Q2ZmZjODM4YmQ4YTBkNDJjNzg5N2I0ZGE1NzMxOGY=',
      APIUsername: 'SB_VTIX_BizAPI',
      APIPassword: 'gfZ35wguKn50uuLS',
      APISignature: '/kOWNOwoB4jEKHa/fjYsAa0Emm1i/WIqdXZdaTy9CDw=',
      base_Url: 'https://newsandbox.payoo.com.vn/v2',
      backend_base_url: 'https://biz-sb.payoo.vn/BusinessRestAPI.svc',
      url_create_payoo: '/create-preorder',
      ipn_url: '/_api/payment/payoo_ipn', //link payoo gọi về khi thành công
      payment_group_bank: 'bank-account',
      payment_group_credit: 'cc',
      payoo_ip_hash: '*************',
    },
    config_vietqr: {
      APICallback: 'https://staging.vtix.vn',
      APIUsername: 'vietqr',
      APIPassword: '12345@789',
      vietqr_username: 'customer-vso16814wsvtix-user24123',
      vietqr_password: 'Y3VzdG9tZXItdnNvMTY4MTR3c3Z0aXgtdXNlcjI0MTIz',
      base_url: 'https://dev.vietqr.org/vqr',
      url_get_token: '/api/token_generate',
      url_test_transaction: '/bank/api/test/transaction-callback',
      url_generate_customer: '/api/qr/generate-customer',
      url_check_order: '/api/transactions/check-order',
      bankAccount: '************',
      userBankName: 'CÔNG TY CỔ PHẦN VTIX MEDIA ENTERTAINMENT',
      bankCode: 'MB',
      uuid_vietqr: 'f9bb2282-aec8-4e45-9600-f04aa823bfe7',
      secret:
        '2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d1003', //secret cấu hình để claim hasura
      expiresIn: 60, //thời gian hết hạn jwt hasura 1 ngày / ngày 60*60*24*1
    },
    config_nft: {
      nft_mainnet: 'https://api.devnet.solana.com',
      nft_address: 'B4Ab1GHdas92yDPetwdNXJmZVdzbzUpFx7EoySFL5XPA',
      nft_pubkey: 'B4Ab1GHdas92yDPetwdNXJmZVdzbzUpFx7EoySFL5XPA',
      nft_keypair:
        '[49,154,127,77,112,195,11,200,4,109,98,141,247,199,10,233,178,197,136,227,201,247,223,242,136,132,172,39,58,85,86,149,149,100,115,112,172,194,35,44,220,109,202,188,246,51,251,52,174,216,163,34,52,102,115,28,207,83,146,39,72,131,87,13]',
      sellerFeeBasisPoints: 100, // phi ban quyen 1%
      nft_symbol_default: 'VTX',
      nft_name_default: 'VTX TICKET',
      nft_image_Default:
        'https://lh3.googleusercontent.com/pw/ADCreHcoLlfpwjKFZGkHHOb9JbFO5zYRJm1-2zVmPDNAcZ8fRBTJQo8FAnloni9x7fOnAVxPYuEoZ-di2aNfRn7V2qvkZaCyTzSVwFJljtp2pY6454ltcgchFliZezrS_qFfq0rCqu8S9LNsuP8e320jBjXu=w1344-h1900-s-no',
    },
    config_email: {
      service: 'smtp',
      // host: 'mail.vtix.vn',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      // email_user: '<EMAIL>',
      // email_password: 'P@ssword!@#123',
      email_user: 'AKIAQ2KT25JEIEKJKTXS',
      email_password: 'BLU3iGTlIg5wgsqM2jl3gcXQDORouIJWzQBOr4a8Qcy2',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_email_marketing: {
      service: 'smtp',
      // host: 'mail.vtix.vn',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      // email_user: '<EMAIL>',
      // email_password: 'P@ssword!@#123',
      email_user: 'AKIAQ2KT25JEIEKJKTXS',
      email_password: 'BLU3iGTlIg5wgsqM2jl3gcXQDORouIJWzQBOr4a8Qcy2',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_email_test: {
      service: 'smtp',
      // host: 'mail.vtix.vn',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      // email_user: '<EMAIL>',
      // email_password: 'P@ssword!@#123',
      email_user: 'AKIAQ2KT25JEIEKJKTXS',
      email_password: 'BLU3iGTlIg5wgsqM2jl3gcXQDORouIJWzQBOr4a8Qcy2',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
  },
  production: {
    protocol: 'https',
    port: 3033,
    database: {
      username: 'vtixprou',
      password: 'NrpbxqIA75KzOiS',
      database: 'vtixprodb',
      host: 'vtixprodb.ci8b13o6lhtk.ap-southeast-1.rds.amazonaws.com',
      dialect: 'postgres',
      replication: {
        read: [
          {
            host: 'vtixprodb-read-replicate.ci8b13o6lhtk.ap-southeast-1.rds.amazonaws.com',
            username: 'vtixprou',
            password: 'NrpbxqIA75KzOiS',
          },
        ],
        write: {
          host: 'vtixprodb.ci8b13o6lhtk.ap-southeast-1.rds.amazonaws.com',
          username: 'vtixprou',
          password: 'NrpbxqIA75KzOiS',
        },
      },
      dialectOptions: {
        ssl: {
          require: true,
          rejectUnauthorized: false,
        },
      },
      port: '5432',
    },
    slash: '/', //if Windows, change this to "\\" & NOT submit to server n
    upload_storage_path: 'file_data/', //MUST be same as declaration of data path in app.js for uploading function
    site_key: 'f90d6859-f5f6', //session secret
    secret: 'f90d6859-f5f6-401f-94ef-40d870f79c8d', //session secret
    pwd_extension: 'Eng',
    timerKeepingSeat: 5 * 60, // 600 second keep seat
    id_vietnam_country: 240,
    gift_users_uid: 'a589d686-d78e-4d74-b0be-2f83437307d7',
    token: {
      secret: 'f90d6859-f5f6-401f-94ef-40d870f79c8d', //session secret
      expiresIn: 30 * 24 * 3600, //seconds
    },
    token_hasura: {
      secret:
        '2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d6009', //secret cấu hình để claim hasura
      expiresIn: 60 * 60 * 24, //thời gian hết hạn jwt hasura 1 ngày / ngày 60*60*24*1
    },
    token_refesh: {
      secret:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmdWxsbmFtZSI6IlRy4bqnbiBNaW5oIFR1eeG6v24iLCJ1c2VySWQiOjE4NCwiYWNjb3VudF9pZCI6MTg2LCJhcHBfaWQiOiJ0dXllbjEyMyIsImRldmljZV9pZCI6InR1eWVuMTIzIiwidGltZV9taSI6MTYyNzI5MTY2NjAwMCwiaWF0IjoxNjI3MjkxNjY2LCJleHAiOjE2Mjk4ODM2NjZ9.vwUSrE4DpGbZw6goRLsaXfTderVdgVZ4ZZVii-B3nw4', //secret cấu hình để claim hasura
      expiresIn: 60 * 60 * 24 * 100, //thời gian hết hạn token refesh 100 ngày || nhưng thường 15 phút hết hạn vì refesh sẽ xóa trong DB
    },
    captchaExpiresIn: 15 * 60,
    config_momo: {
      partner_code: 'MOMOBKUN20180529',
      partner_name: 'Test',
      store_id: 'MOMOBKUN20180529',
      request_type: 'captureWallet',
      ipn_url: '/_api/payment/momo_ipn', //link momo gọi về khi thành công
      secret_key: 'at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa',
      access_key: 'klm05TvNBzhg7h7j',
      base_url: 'https://test-payment.momo.vn', //domain momo
      url_create_momo: '/v2/gateway/api/create',
    },
    config_shopee_pay: {
      merchant_ext_id: '',
      store_ext_id: '',
      client_id: '',
      client_secret: '',
      base_url: 'https://api.wallet.airpay.vn',
      ShopDomain: 'https://vtix.vn',
    },
    config_payoo: {
      BusinessUsername: 'PY8_VTIX_ECOM',
      ShopID: '6643',
      ShopTitle: 'VTIX',
      ShopDomain: 'https://vtix.vn',
      ChecksumKey:
        'yflZSNMJHJ5VFhLrYGo2uFWLkyqypwHOMvHk0LY7S2fkZscX8OlkGBQtolEMU9y6Qr1EPbofXPXQv8h3jlulrQ==',
      APIUsername: 'PY8_VTIX_ECOM_BizAPI',
      APIPassword: 'L03/qaw/E7ZEHfwA',
      APISignature:
        'TWSkLem+d+LYrrBKhnMvPSFaBDLCSHSeC/Jaky3uCUYHoDEOjAHBC6taptBMxeEU',
      base_Url: 'https://payoo.vn/v2',
      backend_base_url: 'ttps://biz.payoo.vn/BusinessRestAPI.svc',
      url_create_payoo: '/create-preorder',
      ipn_url: '/_api/payment/payoo_ipn', //link payoo gọi về khi thành công
      payment_group_bank: 'bank-account',
      payment_group_credit: 'cc',
      payoo_ip_hash: '************',
    },
    config_nft: {
      //nft_mainnet: 'https://api.devnet.solana.com',
      //nft_address: 'B4Ab1GHdas92yDPetwdNXJmZVdzbzUpFx7EoySFL5XPA',
      //nft_pubkey: 'B4Ab1GHdas92yDPetwdNXJmZVdzbzUpFx7EoySFL5XPA',
      //nft_keypair: '[49,154,127,77,112,195,11,200,4,109,98,141,247,199,10,233,178,197,136,227,201,247,223,242,136,132,172,39,58,85,86,149,149,100,115,112,172,194,35,44,220,109,202,188,246,51,251,52,174,216,163,34,52,102,115,28,207,83,146,39,72,131,87,13]',
      sellerFeeBasisPoints: 100, // phi ban quyen 1%
      nft_symbol_default: 'VTX',
      nft_name_default: 'VTX TICKET',
      nft_image_Default:
        'https://lh3.googleusercontent.com/pw/ADCreHcoLlfpwjKFZGkHHOb9JbFO5zYRJm1-2zVmPDNAcZ8fRBTJQo8FAnloni9x7fOnAVxPYuEoZ-di2aNfRn7V2qvkZaCyTzSVwFJljtp2pY6454ltcgchFliZezrS_qFfq0rCqu8S9LNsuP8e320jBjXu=w1344-h1900-s-no',
      //RPC_URL: 'https://api.devnet.solana.com',
      collection_url: 'https://vtix.vn/vi/event/',
      //merkle_tree_pubkey: '8go4gZ2atL7V3ovuhGYDaT3bck9q3dpkgUgXkt6a41Jt',
      //merkle_tree_keypair: '[26,106,11,119,228,176,24,220,118,115,174,253,172,57,137,184,59,1,131,92,193,22,12,103,150,132,169,33,213,116,235,32,114,51,139,79,149,176,68,233,146,221,50,6,77,27,224,50,212,7,163,65,176,157,79,3,100,50,103,10,122,130,119,245]',

      nft_mainnet: 'https://api.mainnet-beta.solana.com',
      nft_address: '5Cjg1S3m9VghwjB8uQJQ9BEonxKbn3EbsF9EEXgQNZQV',
      nft_pubkey: '5Cjg1S3m9VghwjB8uQJQ9BEonxKbn3EbsF9EEXgQNZQV',
      nft_keypair:
        '[116,241,14,4,161,91,155,48,137,5,207,165,79,26,70,169,74,116,155,223,173,33,47,99,5,150,77,66,238,188,66,198,62,112,98,248,97,116,206,142,47,211,5,168,207,27,112,238,160,128,80,65,172,209,52,169,250,149,96,47,246,73,15,234]',

      RPC_URL: 'https://api.mainnet-beta.solana.com',
      merkle_tree_pubkey: '4MXEsrYunQnbDbMi1eXkpjDRvzvwdMXY5ujcqtx3oBQs', // tree that
      merkle_tree_keypair:
        '[194,167,2,226,69,201,158,189,108,165,230,62,188,219,180,193,237,37,91,213,221,132,63,47,248,250,116,221,30,89,217,39,49,212,222,92,67,235,176,94,175,220,42,243,188,187,23,58,20,220,89,114,207,150,111,246,61,7,208,202,17,73,153,64]',
    },
    config_email: {
      service: 'smtp',
      // host: 'mail.vtix.vn',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      email_user: 'AKIAQ2KT25JEIEKJKTXS',
      email_password: 'BLU3iGTlIg5wgsqM2jl3gcXQDORouIJWzQBOr4a8Qcy2',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_email_marketing: {
      service: 'smtp',
      // host: 'mail.vtix.vn',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      email_user: 'AKIAQ2KT25JEIEKJKTXS',
      email_password: 'BLU3iGTlIg5wgsqM2jl3gcXQDORouIJWzQBOr4a8Qcy2',
      base_url: 'http://localhost:5173',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_email_test: {
      service: 'smtp',
      host: 'email-smtp.ap-southeast-1.amazonaws.com',
      port: 465,
      email_sender: 'VTIX <<EMAIL>>',
      email_user: 'AKIAQ2KT25JEMPW6C6OB',
      email_password: 'BA3Gu0x6FVkDwIp6endSN2m3A6MfFkTiPJYtJ0uVOgW4',
      base_url: 'https://vtix.vn',
      banner_vi: '/mail/email_order_confirm_VI.jpg',
      banner_en: '/mail/email_order_confirm_EN.jpg',
      mail_subject: 'VTIX CORP',
    },
    config_vietqr: {
      APICallback: 'https://ws.vtix.vn:3033',
      APIUsername: 'vietqr',
      APIPassword: '12345@789',
      vietqr_username: 'customer-vso16814wsvtix-user2480',
      vietqr_password: 'Y3VzdG9tZXItdnNvMTY4MTR3c3Z0aXgtdXNlcjI0ODA=',
      base_url: 'https://api.vietqr.org/vqr',
      url_get_token: '/api/token_generate',
      url_test_transaction: '/bank/api/test/transaction-callback',
      url_generate_customer: '/api/qr/generate-customer',
      url_check_order: '/api/transactions/check-order',
      bankAccount: '************',
      userBankName: 'CÔNG TY CỔ PHẦN VTIX MEDIA ENTERTAINMENT',
      bankCode: 'MB',
      uuid_vietqr: 'f9bb2282-aec8-4e45-9600-f04aa823bfe7',
      secret:
        '2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d1003', //secret cấu hình để claim hasura
      expiresIn: 60, //thời gian hết hạn jwt hasura 1 ngày / ngày 60*60*24*1
    },
  },
};

const env = process.env.NODE_ENV || 'development';
export default settings[env];
