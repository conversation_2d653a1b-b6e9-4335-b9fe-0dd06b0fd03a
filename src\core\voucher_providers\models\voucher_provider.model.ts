import {
  Table,
  Column,
  Model,
  PrimaryKey,
  DataType,
  Default,
} from 'sequelize-typescript';

@Table({
  tableName: 'voucher_providers',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class VoucherProvider extends Model<VoucherProvider> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Tên nhà cung cấp',
  })
  provider_name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả nhà cung cấp',
  })
  provider_description: string;

  @Column({
    type: DataType.ENUM('internal', 'external'),
    allowNull: false,
    defaultValue: 'internal',
    comment: 'Loại nhà cung cấp: nội bộ hoặc bên ngoài',
  })
  provider_type: 'internal' | 'external';

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Phần trăm hoa hồng',
  })
  commission_rate: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Tổng số voucher phát hành',
  })
  total_amount: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  status: boolean;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  created_at: Date;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;
}
