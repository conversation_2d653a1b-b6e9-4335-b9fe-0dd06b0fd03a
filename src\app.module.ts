import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ConfigModule } from '@nestjs/config';
import * as process from 'process';
import { EventsModule } from '@events/events.module';
import { CNftTempsModule } from '@cnft_temps/cnft_temps.module';
import { TasksModule } from 'tasks/tasks.module';
import { ScheduleModule } from '@nestjs/schedule';
import { StoragesModule } from 'storages/storages.module';
import fileConfig from 'config/file.config';
import { Web3Module } from 'core/web3/web3.module';
import { UsersModule } from '@users/users.module';
import { PaymentsModule } from 'core/payments/payments.module';
import { EventZoneDetailsModule } from '@event_zone_details/event_zone_details.module';
import { UserBookingLogModule } from 'core/user_booking_log/user_booking_log.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [fileConfig],
      envFilePath: ['.env'],
    }),
    SequelizeModule.forRoot({
      dialect: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: process.env.DATABASE_PORT
        ? parseInt(process.env.DATABASE_PORT)
        : 5432,
      username: process.env.DATABASE_USERNAME || 'postgres',
      password: process.env.DATABASE_PASSWORD || 'password',
      database: process.env.DATABASE_NAME || 'vtix',
      autoLoadModels: true,
      synchronize: false,
    }),
    ScheduleModule.forRoot(),
    EventsModule,
    EventZoneDetailsModule,
    CNftTempsModule,
    TasksModule,
    StoragesModule,
    Web3Module,
    UsersModule,
    PaymentsModule,
    UserBookingLogModule,
  ],
})
export class AppModule {}
