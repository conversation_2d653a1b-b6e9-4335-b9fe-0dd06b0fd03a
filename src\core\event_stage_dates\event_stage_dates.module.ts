import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { EventStageDatesService } from './event_stage_dates.services';
import { EventStageDate } from './models/event_stage_date.model';

@Module({
  imports: [SequelizeModule.forFeature([EventStageDate])],
  providers: [EventStageDatesService],
  exports: [EventStageDatesService],
})
export class EventStageDatesModule {}
