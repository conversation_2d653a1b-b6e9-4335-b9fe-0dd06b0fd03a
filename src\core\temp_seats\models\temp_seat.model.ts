import {
  Column,
  Model,
  Table,
  PrimaryKey,
  DataType,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from '@users/models/user.model';
import { Event } from '@events/models/event.model';
import { Transaction } from '@transactions/models/transaction.model';

@Table({
  tableName: 'temp_seats',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class TempSeat extends Model<TempSeat> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => Transaction)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  transaction_id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user cập nhật từ table `users` sau khi được tạo ra.',
  })
  user_id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user đã block ghế',
  })
  block_by_user_id: string;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user parent organizer',
  })
  event_id: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_stage_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_stage_date_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_zone_detail_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_seat_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  seat_number: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  price: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  is_invoice: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  seat_code: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mint_address: string | null;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  nft_transaction: string | null;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  nft_code_order: string | null;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  checked_time: number | null;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  date_use: number;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  // Associations
  @BelongsTo(() => User, {
    foreignKey: 'user_id',
    as: 'user',
  })
  user: User;
}
