import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Event } from '@events/models/event.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';

@Table({
  tableName: 'event_seat_price',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class EventSeatPrice extends Model<EventSeatPrice> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @ForeignKey(() => EventZoneDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event zone',
  })
  zone_detail_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Stage',
  })
  event_stage_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Stage date',
  })
  event_stage_date_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hàng ghế',
  })
  row_code: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Tổng số ghế trên hàng',
  })
  total_seat_row: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Tổng số ghế trên 1 line bao gồm các ghế trống',
  })
  max_seat_row: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Tổng số ghế đã sử dụng',
  })
  seat_used: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Giá hàng ghế',
  })
  price: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Màu sắc',
  })
  color: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.JSON,
    allowNull: false,
    defaultValue: {},
  })
  seat_position: JSON;

  @Column({
    type: DataType.JSON,
    allowNull: false,
    defaultValue: {},
    comment:
      'Tên của table tương ứng với số ghế, Tên table này sẽ thay thế row_code khi thuộc tính group = table_circle',
  })
  seat_position_table_name: JSON;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
    comment: 'Bắt đầu bán ghế từ vị trí thứ',
  })
  seat_pos_start: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Tổng số vé phát hành trong ngày',
  })
  total_seat_today: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  updated_at: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Sắp xếp theo thứ tự hiển thị',
  })
  order_sort: number;

  // Associations
  @BelongsTo(() => EventZoneDetail, {
    foreignKey: 'zone_detail_id',
    as: 'event_zone_detail',
  })
  event_zone_detail: EventZoneDetail;
}
