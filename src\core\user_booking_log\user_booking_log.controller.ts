import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Res,
  Request,
  Query,
  Get,
} from '@nestjs/common';
import { UserBookingLogService } from './user_booking_log.services';
import RestAPI from 'common/rest_api';
import { InternalAPIGuard } from 'guards/internalAPI.guard';

@Controller('')
export class UserBookingLogController {
  constructor(private readonly userBookingLogService: UserBookingLogService) {}

  @Get('/booking-log/get-by-event-id')
  @UseGuards(InternalAPIGuard)
  async getByEventID(
    @Query()
    params: { event_id: string; limit: number },
    @Res() res: Response,
  ): Promise<any> {
    const listData = await this.userBookingLogService.getByEventID(params);
    return RestAPI.success(res, listData);
  }
}
