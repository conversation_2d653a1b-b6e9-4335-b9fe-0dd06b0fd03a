import Constants from './constants';
import fs from 'fs';
import moment from 'moment-timezone';
import * as QRCode from 'qrcode';

// const models = require('../models');
const logPath =
  Constants.DIR_UPLOAD + '/log/log_' + moment().format('YYMMDD') + '.txt';
import config from '../config/setting';
import axios from 'axios';
import _ from 'lodash';
class Common {
  static dLog(message) {
    if (Constants.IS_WRITE_LOG) {
      message =
        moment().format('YYYY-MM-DD HH:mm:ss.SSS') +
        ' ' +
        JSON.stringify(message);
      console.log(message);
      fs.appendFile(logPath, message + '\n', function (err) {});
    }
  }

  static checkValidUUID(uuid) {
    let s = uuid;

    s = s.match(
      '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
    );
    if (s === null) {
      return false;
    }
    return true;
  }

  static generateUuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      },
    );
  }

  static isEmpty(obj) {
    return (
      [Object, Array].includes((obj || {}).constructor) &&
      !Object.entries(obj || {}).length
    );
  }

  static isNull(a_var) {
    return a_var === undefined || a_var === null;
  }

  static checkVerifyParams(params) {
    if (Common.isArray(params)) {
      for (let i = 0; i < params.length; i++) {
        if (Common.isEmpty(params[i])) {
          return false;
        } else {
        }
      }
    } else {
      return !Common.isEmpty(params);
    }

    return true;
  }

  static isArray(something) {
    return Object.prototype.toString.call(something) === '[object Array]';
  }

  static baseUrl(req) {
    // return  req.protocol+"://"+req.headers.host
    return config.protocol + '://' + req.headers.host; //set cứng protocol vì server change port
  }

  static makeResourceURL(req, path) {
    if (path.startsWith('/')) {
      return Common.baseUrl(req) + path;
    } else {
      return Common.baseUrl(req) + '/' + path;
    }
  }

  static convertJsonObject(obj) {
    if (Common.isEmpty(obj)) {
      return null;
    }
    return JSON.parse(JSON.stringify(obj));
  }

  static compareURLAndControler(req, controller) {
    const baseUrlSegment = req.baseUrl.split('/');
    const controllerUrlSegment = controller.url.split('/');
    if (baseUrlSegment.length < controllerUrlSegment.length) {
      return false;
    }

    for (let i = 0; i < controllerUrlSegment.length; i++) {
      if (controllerUrlSegment[i] !== baseUrlSegment[i]) {
        return false;
      }
    }

    return true;
  }

  static weekdayTitle(number, lang = 'vi') {
    number = parseInt(number, 0);
    const weekday = new Array(7);
    if (lang === 'vi') {
      weekday[0] = 'Chủ nhật';
      weekday[1] = 'Thứ 2';
      weekday[2] = 'Thứ 3';
      weekday[3] = 'Thứ 4';
      weekday[4] = 'Thứ 5';
      weekday[5] = 'Thứ 6';
      weekday[6] = 'Thứ 7';
    } else {
      weekday[0] = 'Sunday';
      weekday[1] = 'Monday';
      weekday[2] = 'Tuesday';
      weekday[3] = 'Wednesday';
      weekday[4] = 'Thursday';
      weekday[5] = 'Friday';
      weekday[6] = 'Saturday';
    }

    return number >= 7 ? weekday[6] : weekday[number];
  }
  /*static skipVN(str){

        var plainString = str.toLowerCase();
        plainString = plainString.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
        plainString = plainString.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
        plainString = plainString.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
        plainString = plainString.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
        plainString = plainString.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
        plainString = plainString.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
        plainString = plainString.replace(/đ/g, 'd');
        plainString = plainString.replace(/[^A-Za-z0-9_\s\-]/g, '');
        return plainString;

    }*/
  static skipVN(str) {
    if (!str) {
      return str;
    }
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'a');
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'e');
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'o');
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'u');
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'y');
    str = str.replace(/Đ/g, 'd');
    str = str.toLowerCase();
    return str;
  }
  static makeId(length) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  static findStringInKeyDictionary(str, dictionnary) {
    if (dictionnary) {
      for (const [key, value] of Object.entries(dictionnary)) {
        if (key == str) {
          return true;
        }
      }
    }
    return false;
  }

  //A-Z number start=0=> A
  /*static getCharacter(number){
        return String.fromCharCode(97 + number).toUpperCase();
    }*/
  //A-Z number start=0=> A
  static getCharacter(n) {
    const ordA = 'a'.charCodeAt(0);
    const ordZ = 'z'.charCodeAt(0);
    const len = ordZ - ordA + 1;
    let s = '';
    while (n >= 0) {
      s = String.fromCharCode((n % len) + ordA) + s;
      n = Math.floor(n / len) - 1;
    }
    return s.toUpperCase();
  }
  static firstCodeMomo() {
    return 'MOM';
    //return '';
  }
  static firstCodePAYOO() {
    return 'PAY';
    //return '';
  }
  static firstCodeVIETQR() {
    return 'VQR';
    //return '';
  }
  static firstCodeGIFT() {
    return 'GFT';
    //return '';
  }
  static getRandomNumber(length) {
    const randomChars = '0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += randomChars.charAt(
        Math.floor(Math.random() * randomChars.length),
      );
    }
    return result;
  }

  static getRandomCode(length) {
    const randomChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += randomChars.charAt(
        Math.floor(Math.random() * randomChars.length),
      );
    }
    return result;
  }
  //identification code
  /**
   *
   * @param codePartnert
   * @param centerCode random
   * @param stt
   * @returns {string}
   */
  static indentificationCode(codePartnert, centerCode, stt) {
    return (
      codePartnert + '-' + centerCode + '-' + stt.toString().padStart(9, '0')
    );
  }

  static numberFormat(number, decimals, dec_point, thousands_sep) {
    let n = number;
    const c = isNaN((decimals = Math.abs(decimals))) ? 2 : decimals;
    const d = dec_point == undefined ? ',' : dec_point;
    const t = thousands_sep == undefined ? '.' : thousands_sep,
      s = n < 0 ? '-' : '';
    const i = parseInt((n = Math.abs(+n || 0).toFixed(c))) + '';
    const j = i.length > 3 ? i.length % 3 : 0;

    return (
      s +
      (j ? i.substr(0, j) + t : '') +
      i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + t) +
      (c
        ? d +
          Math.abs(n - parseInt(i))
            .toFixed(c)
            .slice(2)
        : '')
    );
  }
  static sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  static logToFile(title, text, file) {
    // Define file name.
    const filename =
      file !== undefined
        ? moment().format('YYYYMMDD') + '_' + file + '.log'
        : moment().format('YYMMDD') + '_default.log';
    // Define log text.
    let logText = '\r\n';
    logText +=
      '=================================================================================' +
      '\r\n';
    logText +=
      'DATETIME:' +
      ' -> ' +
      moment().format(Constants.DISPLAY_FORMAT_DATETIME_SS) +
      '\r\n';
    logText += 'TITLE:' + ' -> ' + title + '\r\n';
    logText += 'CONTENT:' + ' -> ' + JSON.stringify(text) + '\r\n';
    // Save log to file.
    const directory = Constants.DIR_UPLOAD + '/' + Constants.DIR_UPLOAD_LOG;
    fs.mkdir(directory, { recursive: true }, async (err) => {
      if (err) {
        console.log('ERR CREATE DIRECTORY.');
      }
      //Thành công thì tạo file log
      fs.appendFile(
        directory + '/' + filename,
        logText,
        'utf8',
        function (error) {
          if (error) {
            // If error - show in console.
            console.log('getDateAsText()' + ' -> ' + error);
          }
        },
      );
    });
  }

  static createJsonFile(req, path, text, filename) {
    // Define file name.
    // Save log to file.
    const directory = 'public/' + path;
    fs.mkdir(directory, { recursive: true }, async (err) => {
      if (err) {
        console.log(
          'ERR CREATE DIRECTORY: ' + Constants.DIR_UPLOAD + '/' + path,
        );
      }
      //Thành công thì tạo file log
      fs.writeFile(directory + '/' + filename, text, 'utf8', function (error) {
        if (error) {
          // If error - show in console.
          console.log('getDateAsText()' + ' -> ' + error);
        }
      });
    });

    return (
      config.protocol + '://' + req.headers.host + '/' + path + '/' + filename
    );
  }

  static milisecond() {
    return Math.ceil(moment().tz(Constants.DEFAULT_TIMEZONE).valueOf());
  }

  static getSecond() {
    return Math.ceil(moment().tz(Constants.DEFAULT_TIMEZONE).unix());
  }

  static isEmptyObject(value) {
    if (value === undefined || value === null || value === '') {
      return true;
    } else {
      return Object.keys(value).length === 0 && value.constructor === Object;
    }
  }

  // static async actionUpdateWallet(res, data, t) {
  //   //uid,type,wallet,fee_commission,type_increment
  //   const uid = data['uid'];
  //   const type = data['type'];
  //   let money = data['money'];
  //   const type_increment = data['type_increment'];
  //   const code = data['code'];
  //   const obj_id = data['obj_id'];
  //   const user = await models.users.findOne({
  //     attributes: ['wallet'],
  //     where: {
  //       active: true,
  //       deleted: false,
  //       id: uid,
  //     },
  //     nest: true,
  //   });
  //   // if (!user) {
  //   //     return RestAPI.serverError(res, Constants.MSG.SERVER_ERR)
  //   //     return;
  //   // }
  //   const wallet = user['wallet'];
  //   //type_increment == 1 tăng tiền || 2 trừ tiền
  //   if (type_increment == 1) {
  //     money = parseFloat(money);
  //   } else {
  //     money = -parseFloat(money);
  //   }
  //   const total_wallet = wallet + money;
  //   const walletTransaction: {
  //     uid: string;
  //     type: string;
  //     money: number;
  //     wallet_change: number;
  //     note: string;
  //     code?: string;
  //     obj_id?: string;
  //   } = {
  //     uid: uid,
  //     type: type, //type nạp tiền
  //     money: money,
  //     wallet_change: total_wallet,
  //     note: '',
  //   };
  //   if (!Common.isEmpty(code)) {
  //     walletTransaction.code = code;
  //   }
  //   if (!Common.isEmpty(obj_id)) {
  //     walletTransaction.obj_id = obj_id;
  //   }
  //   const userUpdate = {
  //     wallet: total_wallet,
  //   };
  //   //const t = await models.sequelize.transaction();
  //   await models.users.update(userUpdate, {
  //     where: { id: uid },
  //     transaction: t,
  //   });
  //   await models.wallet_transaction.create(walletTransaction, {
  //     transaction: t,
  //   });
  //
  //   return true;
  // }

  // static async getSetting(key) {
  //   const setting = await models.setting.findOne({
  //     where: {
  //       code: key,
  //     },
  //     nest: true,
  //   });
  //   return setting ? setting['value'] : null;
  // }
  static replaceAll(str, find, replace) {
    const find_char = find.replace(
      /[-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
      '\\$&',
    );
    return str.replace(new RegExp(find_char, 'g'), replace);
  }

  static sortObject(o) {
    let sorted = {},
      key;
    const a: any[] = [];

    for (key in o) {
      if (o.hasOwnProperty(key)) {
        a.push(key);
      }
    }

    a.sort();

    for (key = 0; key < a.length; key++) {
      sorted[a[key]] = o[a[key]];
    }
    return sorted;
  }
  static findValueObject(obj, data_value) {
    let status = false;
    for (const [key, value] of Object.entries(obj)) {
      if (data_value == value) {
        status = true;
        break;
      }
    }
    return status;
  }
  static config_data() {
    return config;
  }
  // static async getFile(type, obj_id = null) {
  //   let file = null;
  //   if (obj_id === null) {
  //     file = await models.relationship_file.findAll({
  //       where: {
  //         type: type,
  //       },
  //       include: [
  //         {
  //           model: models.file,
  //           as: 'file',
  //           required: false,
  //         },
  //       ],
  //       nest: true,
  //     });
  //   } else {
  //     file = await models.relationship_file.findAll({
  //       where: {
  //         obj_id: (obj_id as string).toString(),
  //         type: type,
  //       },
  //       include: [
  //         {
  //           model: models.file,
  //           as: 'file',
  //           required: false,
  //         },
  //       ],
  //       nest: true,
  //     });
  //   }
  //   return file;
  // }

  static removeRespAttrUnuse(obj, data_type) {
    // dataType = 'cart' || 'cart_detail'
    const self = this; // Get a reference to your object
    obj = JSON.parse(JSON.stringify(obj));
    if (Common.isEmpty(obj)) {
      return null;
    } else if (Array.isArray(obj)) {
      const newArray: any[] = [];
      obj.forEach((item) => {
        newArray.push(self.removeRespAttrUnuse(item, data_type));
      });
      return newArray;
    } else if (typeof obj === 'object') {
      let temp = {};
      switch (data_type) {
        case 'cart':
          temp = self.removeCartAttrUnuse(obj);
          break;

        case 'cart_detail':
          temp = self.removeCartDetailAttrUnuse(obj);
          break;

        case 'event':
          temp = self.removeEventAttrUnuse(obj);
          break;

        case 'event_date':
          temp = self.removeEventDateAttrUnuse(obj);
          break;

        case 'event_stage':
          temp = self.removeEventStageAttrUnuse(obj);
          break;

        case 'event_zone':
          temp = self.removeEventZoneAttrUnuse(obj);
          break;

        case 'payment':
          temp = self.removePaymentAttrUnuse(obj);
          break;

        case 'custom_ticket':
          temp = self.removeCustomTicketAttrUnuse(obj);
          break;

        case 'event_seat_price':
          temp = self.removeEventSeatPriceAttrUnuse(obj);
          break;

        default:
          temp = obj;
          break;
      }
      return temp;
    } else {
      return obj;
    }
  }

  static removeCartAttrUnuse(obj) {
    const temp = obj;
    delete temp.code_order;
    delete temp.combo_id;
    delete temp.coupon_id;
    delete temp.created_at;
    delete temp.custom_ticket_id;
    delete temp.deleted;
    // delete temp.event_date_id;
    delete temp.event_id;
    delete temp.event_stage_id;
    delete temp.event_zone_detail_id;
    delete temp.is_done;
    delete temp.uid;
    delete temp.updated_at;
    return temp;
  }

  static removeCartDetailAttrUnuse(obj) {
    const temp = obj;
    delete temp.cart_temp_id;
    delete temp.event_seat_id;
    delete temp.pos;
    delete temp.row_code;
    return temp;
  }

  static removeEventAttrUnuse(obj) {
    const temp = obj;
    delete temp.active;
    delete temp.code;
    delete temp.created_at;
    delete temp.deleted;
    delete temp.is_demo;
    // delete temp.payment_gateways;
    delete temp.uid;
    delete temp.updated_at;
    return temp;
  }

  static removeEventDateAttrUnuse(obj) {
    const temp = obj;
    delete temp.active;
    delete temp.allow_check_in;
    delete temp.created_at;
    delete temp.deleted;
    delete temp.event_id;
    delete temp.event_stage_id;
    delete temp.show_stage;
    delete temp.status;
    delete temp.updated_at;
    return temp;
  }

  static removeEventStageAttrUnuse(obj) {
    const temp = obj;
    delete temp.active;
    delete temp.created_at;
    delete temp.deleted;
    delete temp.event_id;
    delete temp.updated_at;
    return temp;
  }

  static removeEventZoneAttrUnuse(obj) {
    const temp = obj;
    delete temp.active;
    delete temp.created_at;
    delete temp.deleted;
    delete temp.event_date_id;
    delete temp.event_id;
    delete temp.event_stage_id;
    delete temp.nft_image;
    delete temp.nft_name;
    delete temp.nft_symbol;
    delete temp.updated_at;
    return temp;
  }

  static removePaymentAttrUnuse(obj) {
    const temp = obj;
    delete temp.content;
    delete temp.created_at;
    delete temp.id;
    delete temp.obj_id;
    // delete temp.payment_gateways;
    delete temp.status;
    delete temp.status_payoo;
    delete temp.type;
    delete temp.uid;
    delete temp.updated_at;
    return temp;
  }

  static removeCustomTicketAttrUnuse(obj) {
    const temp = obj;
    delete temp.event_id;
    delete temp.event_stage_id;
    delete temp.event_date_id;
    delete temp.active;
    delete temp.updated_at;
    delete temp.created_at;
    delete temp.deleted;
    delete temp.code;
    delete temp.nft_name;
    delete temp.nft_symbol;
    delete temp.nft_image;
    delete temp.zone_detail_id;
    return temp;
  }

  static removeEventSeatPriceAttrUnuse(obj) {
    const temp = obj;
    delete temp.zone_detail_id;
    delete temp.event_id;
    delete temp.event_stage_id;
    delete temp.active;
    delete temp.deleted;
    delete temp.created_at;
    delete temp.updated_at;
    return temp;
  }

  static findNextValueInArray(array, total) {
    const values: number[] = [];
    for (let i = 1; i <= total; i++) {
      if (!array.includes(i)) values.push(i);
    }
    return values;
  }

  static async convertImageToBase64(url) {
    try {
      // Fetch the image data from the URL
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
      });

      // Convert the image data to a base64 string
      const imageBuffer = Buffer.from(response.data, 'binary');

      // if (resize) {
      //   imageBuffer = await sharp(imageBuffer)
      //     .resize(width, height, {
      //       fit: 'cover',
      //       position: 'right',
      //       kernel: sharp.kernel.lanczos3,
      //     })
      //     .toBuffer();
      // }

      const base64Image = imageBuffer.toString('base64');

      // Determine the content type from the response headers
      const contentType = response.headers['content-type'];

      // Return the base64 string with the appropriate data URL prefix
      return `data:${contentType};base64,${base64Image}`;
    } catch (error) {
      console.log('Failed to convert image to base64', url);
      console.log('error', error);
      return '';
    }
  }

  static splitFullName(fullName: string): {
    firstName: string;
    lastName: string;
  } {
    // Trim whitespace and split the name by spaces
    const nameParts = fullName.trim().split(' ');

    if (nameParts.length === 1) {
      // If there's only one part, treat it as the first name
      return { firstName: nameParts[0], lastName: '' };
    }

    // Extract the first name and last name
    const firstName = nameParts[0]; // First part is the first name
    const lastName = nameParts.slice(1).join(' '); // Remaining parts are the last name

    return { firstName, lastName };
  }
  static getDateFormat(
    unixTimestamp: number,
    dateFormat: string = Constants.DB_FORMAT_DATE,
  ): string {
    moment.locale('vi');
    return moment.unix(unixTimestamp).format(dateFormat);
  }

  static renderFullPathImageTicket(dataDetail: any, imagePath: string): string {
    // Tìm category theo group event.
    if (!dataDetail?.event_group_id || null) return '';
    if (!Constants.EVENT_GROUP_DEFINED[dataDetail.event_group_id] || null)
      return '';
    if (imagePath.length === 0) return '';
    const categorySlug: any =
      Constants.EVENT_GROUP_DEFINED[dataDetail.event_group_id].slug;
    return `/${categorySlug}/${_.trimStart(imagePath, '/')}`;
  }

  static createSlug(str: string): string {
    // Define a map for Vietnamese characters
    const vietnameseMap: { [key: string]: string } = {
      à: 'a',
      á: 'a',
      ạ: 'a',
      ả: 'a',
      ã: 'a',
      â: 'a',
      ầ: 'a',
      ấ: 'a',
      ậ: 'a',
      ẩ: 'a',
      ẫ: 'a',
      ă: 'a',
      ằ: 'a',
      ắ: 'a',
      ặ: 'a',
      ẳ: 'a',
      ẵ: 'a',
      đ: 'd',
      è: 'e',
      é: 'e',
      ẹ: 'e',
      ẻ: 'e',
      ẽ: 'e',
      ê: 'e',
      ề: 'e',
      ế: 'e',
      ệ: 'e',
      ể: 'e',
      ễ: 'e',
      ì: 'i',
      í: 'i',
      ị: 'i',
      ỉ: 'i',
      ĩ: 'i',
      ò: 'o',
      ó: 'o',
      ọ: 'o',
      ỏ: 'o',
      õ: 'o',
      ô: 'o',
      ồ: 'o',
      ố: 'o',
      ộ: 'o',
      ổ: 'o',
      ỗ: 'o',
      ơ: 'o',
      ờ: 'o',
      ớ: 'o',
      ợ: 'o',
      ở: 'o',
      ỡ: 'o',
      ù: 'u',
      ú: 'u',
      ụ: 'u',
      ủ: 'u',
      ũ: 'u',
      ư: 'u',
      ừ: 'u',
      ứ: 'u',
      ự: 'u',
      ử: 'u',
      ữ: 'u',
      ỳ: 'y',
      ý: 'y',
      ỵ: 'y',
      ỷ: 'y',
      ỹ: 'y',
    };

    // Convert Vietnamese characters
    str = str
      .toLowerCase()
      .replace(
        /[àáạảãâầấậẩẫăằắặẳẵđèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹ]/g,
        (match) => vietnameseMap[match],
      );

    // Remove non-alphanumeric characters except spaces and hyphens
    str = str.replace(/[^a-z0-9 -]/g, '');

    // Replace spaces with hyphens and remove consecutive hyphens
    return str.trim().replace(/\s+/g, '-').replace(/-+/g, '-');
  }

  static findNextNumberSeats(seat_position, listSeatNumber, newQuantity) {
    const availableSeats = seat_position.filter(
      (seat) => !listSeatNumber.includes(seat),
    );
    const nextSeats = availableSeats.slice(0, newQuantity);
    return nextSeats;
  }

  static async generateQrCode(text: string): Promise<string> {
    try {
      const qrCodeDataUrl = await QRCode.toDataURL(text, {
        margin: 0, // 👈 Xóa hoàn toàn viền trắng
      });
      return qrCodeDataUrl; // là một chuỗi base64: "data:image/png;base64,..."
    } catch (err) {
      throw new Error('Không thể tạo QR code: ' + err.message);
    }
  }
}

export default Common;
