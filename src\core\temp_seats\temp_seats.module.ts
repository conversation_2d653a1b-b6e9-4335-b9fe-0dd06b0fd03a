import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TempSeat } from './models/temp_seat.model';
import { TempSeatsService } from './temp_seats.services';
import { EventStageDatesModule } from '@event_stage_dates/event_stage_dates.module';

@Module({
  imports: [SequelizeModule.forFeature([TempSeat]), EventStageDatesModule],
  providers: [TempSeatsService],
  exports: [TempSeatsService],
})
export class TempSeatsModule {}
