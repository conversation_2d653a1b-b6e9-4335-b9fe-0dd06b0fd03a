import { Module } from '@nestjs/common';
import { PaymentsController } from './payments.controller';
import { PaymentsService } from './payments.services';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '@users/models/user.model';
import { Event } from '@events/models/event.model';
import { EventsModule } from '@events/events.module';
import { EventStagesModule } from '@event_stages/event_stages.module';
import { EventStageDatesModule } from '@event_stage_dates/event_stage_dates.module';
import { EventZoneDetailsModule } from '@event_zone_details/event_zone_details.module';
import { CouponsModule } from '@coupons/coupons.module';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import { EventSeatPricesModule } from '@event_seat_prices/event_seat_prices.module';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { Coupon } from '@coupons/models/coupon.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      Event,
      EventStage,
      EventStageDate,
      EventZoneDetail,
      PaymentDetail,
      Coupon,
    ]),
    EventsModule,
    EventStagesModule,
    EventStageDatesModule,
    EventZoneDetailsModule,
    CouponsModule,
    EventSeatPricesModule,
  ],
  providers: [PaymentsService],
  controllers: [PaymentsController],
  exports: [PaymentsService],
})
export class PaymentsModule {}
