import { InjectModel } from '@nestjs/sequelize';
import RestAPI from 'common/rest_api';
import { Sequelize } from 'sequelize-typescript';
import { TempQuantity } from './models/temp_quantity.model';
import { BOOKING_MAX_QUANTITY } from 'config/constant';
import { EventSeatPricesService } from '@event_seat_prices/event_seat_prices.services';
import { EventStageDatesService } from '@event_stage_dates/event_stage_dates.services';
import Common from 'common/common';
import _ from 'lodash';
import { User } from '@users/models/user.model';
import { EventZoneDetailsService } from '@event_zone_details/event_zone_details.services';
import { Op } from 'sequelize';
export class TempQuantitiesService {
  constructor(
    @InjectModel(TempQuantity)
    private readonly tempQuantityModel: typeof TempQuantity,

    private eventSeatPricesService: EventSeatPricesService,
    private eventStageDatesService: EventStageDatesService,
    private eventZoneDetailService: EventZoneDetailsService,

    private sequelize: Sequelize,
  ) {}

  async getListTempQuantityUnPaidByTransactionId(transaction_id) {
    return await this.tempQuantityModel.findAll({
      where: {
        transaction_id,
        is_invoice: false,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  async getListTempQuantityByTransactionId(transaction_id, date_use = 0) {
    const condition = {
      transaction_id,
      mint_address: {
        [Op.is]: null,
      },
    };
    if (date_use && date_use > 0) {
      _.assign(condition, { date_use: date_use });
    }

    return await this.tempQuantityModel.findAll({
      where: condition,
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  async getTempQuantityByTransactionId(transaction_id) {
    return await this.tempQuantityModel.findOne({
      where: {
        transaction_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  async getTempQuantityDetail(condtions: Partial<TempQuantity>) {
    return await this.tempQuantityModel.findOne({
      where: {
        ...condtions,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async bulkCreate(payload: any, res: Response): Promise<any> {
    const {
      transaction_id,
      event_id,
      user_id,
      event_stage_id,
      event_stage_date_id,
      event_zone_detail_id,
      event_seat_id,
      quantity,
      seat_code,
      price,
      date_use = 0,
    } = payload;
    const t = await this.sequelize.transaction();

    const eventStageDateDetail =
      await this.eventStageDatesService.getEventStageDateDetail(
        event_stage_date_id,
      );
    if (!eventStageDateDetail) {
      t.commit();
      return { result: false, data: 'EVENT_DATE_NOT_EXIST' };
    }

    if (quantity <= 0) {
      const delConditions = {
        transaction_id,
        user_id,
      };
      if (payload.event_zone_detail_id !== 0) {
        delConditions['event_zone_detail_id'] = payload.event_zone_detail_id;
      }
      await this.removeTempQuantity({ ...delConditions });
      t.commit();
      return {
        result: true,
        data: {
          code: 'UPDATE_BOOK_SEAT_QUANTITY_SUCCESS',
        },
      };
    } else if (quantity > eventStageDateDetail.max_order) {
      // Dùng để check số lớn hơn quy định
      t.commit();
      return { result: true, data: { code: 'BOOK_SEAT_QUANTITY_TO_MAX' } };
    }

    try {
      const eventSeatPriceRecord =
        await this.eventSeatPricesService.getEventSeatPriceDetail(
          event_seat_id,
        );

      if (!eventSeatPriceRecord) {
        t.commit();
        return { result: false, data: 'EVENT_SEAT_NOT_EXIST' };
      }

      const seat_pos_start = eventSeatPriceRecord.seat_pos_start;
      let seat_position: any = eventSeatPriceRecord.seat_position;
      seat_position = Object.values(seat_position).map((value: string) =>
        parseInt(value),
      );
      console.log('----------seat_position :>> ', seat_position);

      const eventZoneDetail =
        await this.eventZoneDetailService.getEventZoneDetail(
          event_zone_detail_id,
        );
      const buyQuantityRequired = eventZoneDetail?.buy_required || 0;
      const getFreeQuantity = eventZoneDetail?.get_free_quantity || 0;

      const listTempQuantities = await this.tempQuantityModel.findAll({
        where: {
          event_id,
          event_stage_id,
          event_stage_date_id,
          event_zone_detail_id,
          event_seat_id,
          date_use,
        },
        raw: true,
        transaction: t,
      });

      // Tổng số vé dc phát hành trong ngày
      const total_seat_today = eventSeatPriceRecord.total_seat_today;

      if (!_.isEmpty(listTempQuantities)) {
        const listSeatNumber = listTempQuantities
          .map((val) => val.seat_number)
          .sort();
        // Tìm list temp quantity theo user
        // Check với số quantity FE truyền lên so với list temp theo user
        // Nếu lớn hơn hoặc chưa có là case add, nhỏ hơn là case giảm
        //  1. Nếu là case add thì tính total Quantity = current quantity + (list quantity theo user  - quantity fe truyền)
        //    - Nếu total quantity > total seat row thì trả lỗi
        //    - Nếu thỏa thì tính next Value và create
        //  2. Nếu là case giảm thì lấy phần tử cuối của list temp quantity theo user ra giảm
        const listTempQuantityByUser = listTempQuantities.filter(
          (val) =>
            val.user_id === user_id &&
            val.transaction_id === transaction_id &&
            val.price !== 0 &&
            parseInt(val.event_zone_detail_id.toString()) ===
              event_zone_detail_id &&
            val.date_use === date_use,
        );

        if (listTempQuantityByUser.length > 0) {
          let newQuantity = quantity - listTempQuantityByUser.length;
          console.log('----newQuantity', newQuantity);

          if (newQuantity > 0) {
            const currentQuantity = listTempQuantities.length;
            const totalQuantity = currentQuantity + newQuantity;

            // Logic check số lượng vé mua vs tổng số vé phát hành trong ngày
            if (total_seat_today > 0 && total_seat_today < totalQuantity) {
              t.commit();
              return {
                result: true,
                data: { code: 'BOOK_SEAT_QUANTITY_TODAY_TO_MAX' },
              };
            }

            if (totalQuantity > eventSeatPriceRecord.total_seat_row) {
              t.commit();
              return {
                result: true,
                data: { code: 'BOOK_SEAT_QUANTITY_EXCEED' },
              };
            }

            // Xử lí trường hợp vé promos/vé tặng theo logic buy x free x
            const { totalFreeTickets, totalTickets, message_code } =
              this.calculatePromotionTickets(
                buyQuantityRequired,
                getFreeQuantity,
                quantity,
                eventSeatPriceRecord.total_seat_row,
                listTempQuantities.length,
              );
            if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
              console.log('------if------', {
                totalFreeTickets,
                totalTickets,
                message_code,
              });
              if (totalTickets < eventSeatPriceRecord.total_seat_row) {
                const listTempQuantityPromosByUser = listTempQuantities.filter(
                  (val) =>
                    val.user_id === user_id &&
                    val.price === 0 &&
                    parseInt(val.event_zone_detail_id.toString()) ===
                      event_zone_detail_id &&
                    val.date_use === date_use,
                );
                // Tính tính tổng vé promos đã xuất
                const totalPromosTickets =
                  totalFreeTickets - listTempQuantityPromosByUser.length;
                if (totalPromosTickets > 0) {
                  newQuantity = newQuantity + getFreeQuantity; // Cộng thêm số lượng vé free
                }
              }
            }

            const listNextSeatNumber = Common.findNextNumberSeats(
              seat_position,
              listSeatNumber,
              newQuantity,
            );

            const dataCreateTempQuantities: any = listNextSeatNumber.map(
              (nextValue, i) => ({
                transaction_id,
                event_id,
                user_id,
                event_stage_id,
                event_stage_date_id,
                event_zone_detail_id,
                event_seat_id,
                price: i === 0 ? price : 0, // Vé promos price sẽ là 0
                seat_number: nextValue,
                seat_code:
                  seat_code && seat_code != undefined && seat_code.length > 0
                    ? `${seat_code}-${nextValue}`
                    : nextValue,
                date_use,
              }),
            );
            await this.tempQuantityModel.bulkCreate(dataCreateTempQuantities, {
              transaction: t,
            });
            // Check hết vé promos để thông báo
            if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
              if (message_code === 'PROMOTION_SOLD_OUT') {
                t.commit();
                return {
                  result: true,
                  data: { code: 'BOOK_SEAT_QUANTITY_PROMOS_EXCEED' },
                };
              }
            }
          } else {
            // Xóa vé promos cùng vé giảm
            if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
              const listTempQuantityByUserAll = listTempQuantities.filter(
                (val) =>
                  val.user_id === user_id &&
                  val.transaction_id === transaction_id &&
                  parseInt(val.event_zone_detail_id.toString()) ===
                    event_zone_detail_id &&
                  val.date_use === date_use,
              );
              await this.processRemoveQuantityWithPromos(
                listTempQuantityByUserAll,
                buyQuantityRequired,
                getFreeQuantity,
              );
            } else {
              const lastElement = listTempQuantityByUser.pop();
              await this.removeTempQuantity({
                id: lastElement!.id,
              });
            }
          }

          t.commit();
          return {
            result: true,
            data: {
              code: 'BOOK_SEAT_QUANTITY_SUCCESS',
            },
          };
        } else {
          // Logic check số lượng vé mua vs tổng số vé phát hành trong ngày
          if (
            total_seat_today > 0 &&
            total_seat_today <= listTempQuantities.length
          ) {
            t.commit();
            return {
              result: true,
              data: { code: 'BOOK_SEAT_QUANTITY_TODAY_TO_MAX' },
            };
          }

          // Kiểm tra đã hết vé
          if (
            eventSeatPriceRecord.total_seat_row <
            listTempQuantities.length + 1
          ) {
            t.commit();
            return {
              result: true,
              data: { code: 'BOOK_SEAT_QUANTITY_EXCEED' },
            };
          }
          let numberOfQuantity = quantity;
          // Xử lí trường hợp vé promos/vé tặng theo logic buy x free x
          const { totalFreeTickets, totalTickets, message_code } =
            this.calculatePromotionTickets(
              buyQuantityRequired,
              getFreeQuantity,
              quantity,
              eventSeatPriceRecord.total_seat_row,
              listTempQuantities.length,
            );
          if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
            console.log('------else------', {
              totalFreeTickets,
              totalTickets,
              message_code,
            });
            if (totalTickets < eventSeatPriceRecord.total_seat_row) {
              const listTempQuantityPromosByUser = listTempQuantities.filter(
                (val) =>
                  val.user_id === user_id &&
                  val.price === 0 &&
                  parseInt(val.event_zone_detail_id.toString()) ===
                    event_zone_detail_id &&
                  val.date_use === date_use,
              );
              // Tính tổng vé promos đã xuất
              const totalPromosTickets =
                totalFreeTickets - listTempQuantityPromosByUser.length;
              if (totalPromosTickets > 0) {
                numberOfQuantity = numberOfQuantity + getFreeQuantity;
              }
            }
          }
          const listNextSeatNumber = Common.findNextNumberSeats(
            seat_position,
            listSeatNumber,
            numberOfQuantity,
          );
          const dataCreateTempQuantities: any = listNextSeatNumber.map(
            (nextValue, i) => ({
              transaction_id,
              event_id,
              user_id,
              event_stage_id,
              event_stage_date_id,
              event_zone_detail_id,
              event_seat_id,
              price: i === 0 ? price : 0, // Vé promos price sẽ là 0
              seat_number: nextValue,
              seat_code:
                seat_code && seat_code != undefined && seat_code.length > 0
                  ? `${seat_code}-${nextValue}`
                  : nextValue,
              date_use,
            }),
          );
          await this.tempQuantityModel.bulkCreate(dataCreateTempQuantities, {
            transaction: t,
          });

          t.commit();
          // Check hết vé promos để thông báo
          if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
            if (message_code === 'PROMOTION_SOLD_OUT') {
              return {
                result: true,
                data: { code: 'BOOK_SEAT_QUANTITY_PROMOS_EXCEED' },
              };
            }
          }

          return {
            result: true,
            data: {
              code: 'BOOK_SEAT_QUANTITY_SUCCESS',
            },
          };
        }
      } else {
        let numOfQuantity = quantity;

        // Logic check số lượng vé mua vs tổng số vé phát hành trong ngày
        if (total_seat_today > 0 && total_seat_today < numOfQuantity) {
          t.commit();
          return {
            result: true,
            data: { code: 'BOOK_SEAT_QUANTITY_TODAY_TO_MAX' },
          };
        }

        // Xử lí trường hợp vé promos/vé tặng theo logic buy x free x
        const { totalTickets, message_code } = this.calculatePromotionTickets(
          buyQuantityRequired,
          getFreeQuantity,
          quantity,
          eventSeatPriceRecord.total_seat_row,
          0,
        );
        if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
          if (totalTickets < eventSeatPriceRecord.total_seat_row) {
            numOfQuantity = totalTickets;
          }
        }
        const dataCreateTempQuantities: any = Array.from(
          { length: numOfQuantity },
          (_, i) => ({
            transaction_id,
            event_id,
            user_id,
            event_stage_id,
            event_stage_date_id,
            event_zone_detail_id,
            event_seat_id,
            price: i === 0 ? price : 0, // Vé promos price sẽ là 0
            seat_number: seat_pos_start + i,
            seat_code:
              seat_code && seat_code != undefined && seat_code.length > 0
                ? `${seat_code}-${seat_pos_start + i}`
                : seat_pos_start + i,
            date_use,
          }),
        );
        await this.tempQuantityModel.bulkCreate(dataCreateTempQuantities, {
          transaction: t,
        });

        t.commit();

        // Check hết vé promos để thông báo
        if (buyQuantityRequired !== 0 && getFreeQuantity !== 0) {
          if (message_code === 'PROMOTION_SOLD_OUT') {
            return {
              result: true,
              data: { code: 'BOOK_SEAT_QUANTITY_PROMOS_EXCEED' },
            };
          }
        }

        return {
          result: true,
          data: {
            code: 'BOOK_SEAT_QUANTITY_SUCCESS',
          },
        };
      }
    } catch (error) {
      t.rollback();
      return { result: false, data: error.message };
    }
  }

  calculatePromotionTickets(
    buyQuantityRequired,
    getFreeQuantity,
    totalTicketsToBuy,
    totalTicketsAvailable,
    totalTicketsSold,
  ) {
    if (totalTicketsToBuy <= 0) {
      return {
        numberOfPromotionsApplied: 0,
        totalFreeTickets: 0,
        totalTickets: 0,
        message: 'Số lượng vé mua phải lớn hơn 0',
        message_code: 'INVALID_QUANTITY',
      };
    }

    if (totalTicketsAvailable <= totalTicketsSold) {
      return {
        numberOfPromotionsApplied: 0,
        totalFreeTickets: 0,
        totalTickets: 0,
        message: 'Đã hết vé',
        message_code: 'SOLD_OUT',
      };
    }

    // Tính số vé còn lại
    const ticketsRemaining = totalTicketsAvailable - totalTicketsSold;
    let curQuantity = totalTicketsToBuy - totalTicketsSold;
    curQuantity = curQuantity < 0 ? 1 : curQuantity;

    // Kiểm tra xem số vé mua có vượt quá số vé còn lại hay không
    // if (totalTicketsToBuy > ticketsRemaining) {
    if (curQuantity > ticketsRemaining) {
      return {
        numberOfPromotionsApplied: 0,
        totalFreeTickets: 0,
        totalTickets: 0,
        message: 'Số vé mua vượt quá số vé còn lại',
        message_code: 'EXCEED_REMAINING_TICKETS',
      };
    }

    // Tính số lần áp dụng khuyến mãi dựa trên số vé mua
    let numberOfPromotionsApplied = Math.floor(
      totalTicketsToBuy / buyQuantityRequired,
    );
    let totalFreeTickets = numberOfPromotionsApplied * getFreeQuantity;
    let totalTickets = curQuantity + totalFreeTickets;
    // Kiểm tra xem tổng số vé (mua + tặng) có vượt quá số vé còn lại hay không
    if (totalTicketsSold + totalTickets > totalTicketsAvailable) {
      // Điều chỉnh số vé tặng để không vượt quá số vé còn lại
      totalFreeTickets = ticketsRemaining - totalTicketsToBuy;
      totalFreeTickets = Math.max(0, totalFreeTickets); // Đảm bảo không âm
      numberOfPromotionsApplied =
        getFreeQuantity > 0
          ? Math.floor(totalFreeTickets / getFreeQuantity)
          : 0;
      totalTickets = totalTicketsToBuy + totalFreeTickets;

      return {
        numberOfPromotionsApplied: numberOfPromotionsApplied,
        totalFreeTickets: totalFreeTickets,
        totalTickets: totalTickets,
        message: 'Khuyến mãi đã hết',
        message_code: 'PROMOTION_SOLD_OUT',
      };
    }

    return {
      numberOfPromotionsApplied: numberOfPromotionsApplied,
      totalFreeTickets: totalFreeTickets,
      totalTickets: totalTickets,
      message: 'Khuyến mãi áp dụng thành công',
      message_code: 'PROMOTION_APPLIED',
    };
  }

  async processRemoveQuantityWithPromos(
    listTempQuantityByUserAll,
    buyCount,
    freeCount,
  ) {
    const paidItems = listTempQuantityByUserAll.filter(
      (item) => parseInt(item.price) > 0,
    ); // Paid items
    const freeItems = listTempQuantityByUserAll.filter(
      (item) => parseInt(item.price) === 0,
    ); // Free items

    // Calculate how many free items the user qualifies for
    const qualifiedFreeItems =
      Math.floor(paidItems.length / buyCount) * freeCount;

    if (freeItems.length > qualifiedFreeItems) {
      // If there are more free items than the user qualifies for, remove 1 free item
      const lastFreeItem = freeItems[freeItems.length - 1];
      if (lastFreeItem) {
        await this.removeTempQuantity({
          id: lastFreeItem.id,
        });
        console.log(`Removed free item with ID: ${lastFreeItem.id}`);
      }
    } else {
      // Otherwise, remove 1 paid item
      const lastPaidItem = paidItems[paidItems.length - 1];
      if (lastPaidItem) {
        await this.removeTempQuantity({
          id: lastPaidItem.id,
        });
        console.log(`Removed paid item with ID: ${lastPaidItem.id}`);
      }

      // Recalculate qualified free items after removing the paid item
      const newQualifiedFreeItems =
        Math.floor((paidItems.length - 1) / buyCount) * freeCount;

      // If the number of free items now exceeds the new qualification, remove 1 free item
      if (freeItems.length > newQualifiedFreeItems) {
        const lastFreeItem = freeItems[freeItems.length - 1];
        if (lastFreeItem) {
          await this.removeTempQuantity({
            id: lastFreeItem.id,
          });
          console.log(`Removed free item with ID: ${lastFreeItem.id}`);
        }
      }
    }
  }

  async removeTempQuantity(conditions: Partial<TempQuantity>): Promise<any> {
    const t = await this.sequelize.transaction();
    try {
      await this.tempQuantityModel.destroy({
        where: {
          ...conditions,
          is_invoice: false,
        },
        transaction: t,
      });
      await t.commit();
    } catch (error) {
      t.rollback();
    }
  }

  async removeTempQuantityByTransactionIds(transaction_ids): Promise<any> {
    const t = await this.sequelize.transaction();
    try {
      await this.tempQuantityModel.destroy({
        where: {
          transaction_id: transaction_ids,
          is_invoice: false,
        },
        transaction: t,
      });
      await t.commit();
    } catch (error) {
      t.rollback();
    }
  }

  // Tìm booking quantity
  async findOneHolding(payload: any, res: Response): Promise<any> {
    try {
      const existingRecord = await this.tempQuantityModel.findAll({
        where: {
          event_id: payload.event_id,
          event_stage_id: payload.event_stage_id,
          event_stage_date_id: payload.event_stage_date_id,
          transaction_id: payload.transaction_id,
          user_id: payload.user_id,
          is_invoice: false,
          price: {
            [Op.gt]: 0,
          },
        },
        raw: true,
      });
      return existingRecord;
    } catch (error) {
      throw error;
    }
  }

  // Tìm vé được tặng
  async findTicketGift(payload: any, res: Response): Promise<any> {
    try {
      const existingRecord = await this.tempQuantityModel.findAll({
        where: {
          event_id: payload.event_id,
          event_stage_id: payload.event_stage_id,
          event_stage_date_id: payload.event_stage_date_id,
          transaction_id: payload.transaction_id,
          user_id: payload.user_id,
          is_invoice: false,
          price: {
            [Op.eq]: 0,
          },
        },
        raw: true,
      });
      return existingRecord;
    } catch (error) {
      throw error;
    }
  }

  async getListTempQuantityByCondition(conditions, includes = false) {
    const query: any = {
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    };

    if (includes) {
      query.include = [
        {
          model: User,
          as: 'user',
          required: true,
        },
      ];
    }

    return await this.tempQuantityModel.findAll(query);
  }
}
