import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import config from '../config/setting';
import * as path from 'path';
import * as ejs from 'ejs';
import nodemailer from 'nodemailer';
import { UsersService } from '@users/users.services';
import { firstValueFrom } from 'rxjs';
import _ from 'lodash';

@Command({
  name: 'process-send-email-marketing',
  description: 'Process send email marketing with customize templated email',
})
/**
 * Gửi email marketing
 * chỉ định chính xác template cần gửi tại thư mục views/mail/marketing/...
 */
@Injectable()
export class SendEmailMarketingCommand extends CommandRunner {
  private readonly logger = new Logger(SendEmailMarketingCommand.name);
  private timeDelayNextZone = 30 * 1000; // 30 seconds

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Guide:
  // VD: npx ts-node src/cli.ts process-send-email-marketing --template="hot_events" --approve=true
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const template = options?.template;
    const approve = options?.approve;
    console.info({
      message: 'Call email template: ',
      template,
      approve,
    });

    const defineSubjectTitle =
      '💥 Sự kiện HOT nhất mùa hè tại VTIX – Ưu đãi giới hạn, chốt vé ngay!';

    const data: any = {
      special: [
        {
          image: 'https://www.vtix.vn/event/unicorn/con-mua-ha/poster.webp',
          title:
            'MINI TOURS SHOW "CƠN MƯA HẠ" - NGÔI SAO HẢI NGOẠI LÂM THUÝ VÂN',
          // date: '',
          // hour: '',
          address1: 'Phòng trà Không Tên',
          address2: 'Số 112 Lê Thánh Tôn, Tp.HCM',
          date: '20:30, Thứ tư, Ngày 06/08/2025', // Ngày: 04, 05, 06/07/2025
          hour: null,
          link: 'https://www.vtix.vn/vi/event/mini-tours-show-con-mua-ha-ngoi-sao-hai-ngoai-lam-thuy-van',
          couponDes: null, // '<span style="font-weight: 400" class="color-black">NHẬP MÃ - </span> <strong class="color-black">SALE7</strong> <span style="font-weight: 400" class="color-black">ĐỂ NHẬN ƯU ĐÃI NGAY</span>',
        },
        {
          image:
            'https://www.vtix.vn/event/unicorn/tia-nang-trong-anh/poster.webp',
          title:
            'LIVE MUSIC "VỀ ĐÂY EM LÀ TIA NẮNG TRONG ANH" - NGÔI SAO HẢI NGOẠI LÂM THUÝ VÂN',
          // date: '',
          // hour: '',
          address1: 'Trung tâm hội nghị Âu Lạc Thịnh',
          address2: 'Số 99 Nguyễn Thị Minh Khai, Nha Trang',
          date: '20:15, Thứ năm, Ngày 07/08/2025', // Ngày: 04, 05, 06/07/2025
          hour: null,
          link: 'https://www.vtix.vn/vi/event/live-music-ve-day-em-la-tia-nang-trong-anh-ngoi-sao-hai-ngoai-lam-thuy-van',
          couponDes: null, // '<span style="font-weight: 400" class="color-black">NHẬP MÃ - </span> <strong class="color-black">SALE7</strong> <span style="font-weight: 400" class="color-black">ĐỂ NHẬN ƯU ĐÃI NGAY</span>',
        },
        {
          image:
            'https://www.vtix.vn/event/unicorn/nhac-tinh-muon-thuo/poster.webp',
          title:
            'LIVESHOW "NHẠC TÌNH MUÔN THUỞ" - NGÔI SAO HẢI NGOẠI MẠNH QUỲNH & LÂM THUÝ VÂN',
          // date: '',
          // hour: '',
          address1: 'URI PALACE BẮC NINH',
          address2: '23 Lê Thái Tổ, Võ Cường, Bắc Ninh',
          date: '20:00, Thứ bảy, Ngày 09/08/2025', // Ngày: 04, 05, 06/07/2025
          hour: null,
          link: 'https://www.vtix.vn/vi/event/liveshow-nhac-tinh-muon-thuo-ngoi-sao-hai-ngoai-manh-quynh-lam-thuy-van',
          couponDes: null, //'<span style="font-weight: 400" class="color-black">NHẬP MÃ - </span> <strong class="color-black">SALE7</strong> <span style="font-weight: 400" class="color-black">ĐỂ NHẬN ƯU ĐÃI NGAY</span>',
        },
        {
          image: 'https://www.vtix.vn/event/unicorn/con-mua-ha-2/poster.webp',
          title:
            'MINI TOURS SHOW "CƠN MƯA HẠ" - NGÔI SAO HẢI NGOẠI LÂM THUÝ VÂN',
          // date: '',
          // hour: '',
          address1: 'Sky Lounge, 36 Hoàng Cầu,',
          address2: '36A Bán Đảo Hoàng Cầu, Hà Nội',
          date: '20:30, Thứ năm, Ngày 14/08/2025', // Ngày: 04, 05, 06/07/2025
          hour: null,
          link: 'https://www.vtix.vn/vi/event/mini-tours-show-con-mua-ha-ngoi-sao-hai-ngoai-lam-thuy-van-14-08-2025',
          couponDes: null, // '<span style="font-weight: 400" class="color-black">NHẬP MÃ - </span> <strong class="color-black">SALE7</strong> <span style="font-weight: 400" class="color-black">ĐỂ NHẬN ƯU ĐÃI NGAY</span>',
        },
      ],
      hot: [
        {
          image:
            'https://www.vtix.vn/travel/kongforest/zipline-atv/poster.webp',
          title:
            'COMBO GAME: CHINH PHỤC ZIPLINE CANOPY & ATV TẠI KONG FOREST NHA TRANG',
          address: 'Thời lượng: 120 – 150 phút', //'Thành phố Gia Nghĩa, Đắk Nông',
          fullDate: 'Khởi hành lúc 08:00 và 10:45 mỗi ngày',
          link: 'https://www.vtix.vn/vi/travel/combo-game-chinh-phuc-zipline-canopy-atv-tai-kong-forest-nha-trang',
          coupon: 'AMAZING',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã AMAZING giảm 5% </strong> <span style="font-weight: 400" class="color-black">(không giới hạn số lần dùng)</span>',
        },
        {
          image:
            'https://www.vtix.vn/travel/kongforest/zipline-canopy/poster.webp',
          title: 'Giải chạy Liên đoàn Lao động TP.HCM, Labour Run 2025',
          address:
            'Trải nghiệm bay rừng đơn thuần với hệ thống zipline 13 trạm – zipline đôi, zipline đơn, cầu xoắn, cầu xe lửa với nhiều thử thách và trải nghiệm thú vị.', //'Công viên bờ sông Sài Gòn - TP. Thủ Đức, TP.HCM',
          fullDate: 'Khởi hành lúc 08:00 và 10:45 mỗi ngày',
          link: 'https://www.vtix.vn/vi/travel/trai-nghiem-kham-pha-zipline-canopy-tai-kong-forest-nha-trang',
          coupon: 'AMAZING',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã AMAZING giảm 5% </strong> <span style="font-weight: 400" class="color-black">tương tự combo</span>',
        },
        {
          image:
            'https://www.vtix.vn/travel/kongforest/atv-extreme/poster.webp',
          title:
            'TRẢI NGHIỆM LÁI XE ĐỊA HÌNH ATV EXTREME TẠI KONG FOREST NHA TRANG',
          address:
            'Hoạt động: Lái xe địa hình băng rừng – trải nghiệm cảm giác mạnh',
          fullDate: 'Khởi hành lúc 08:00 và 10:45 mỗi ngày',
          link: 'https://www.vtix.vn/vi/travel/trai-nghiem-lai-xe-dia-hinh-atv-extreme-tai-kong-forest-nha-trang',
          coupon: 'AMAZING',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã AMAZING giảm 5% </strong> <span style="font-weight: 400" class="color-black">không giới hạn số lần sử dụng</span>',
        },
        {
          image:
            'https://www.vtix.vn/travel/expeditions/abseiling-va-kham-pha-ho-hang-vem/poster.webp',
          title: 'Abseiling và Khám phá Hồ Hang Vẹm (Tour nữa ngày Cát Bà)',
          address:
            'Tour VNE – Vietnam Expeditions (Thám hiểm Cát Bà, Lạng Sơn)',
          fullDate: 'Khởi hành thứ 7, chủ nhật hàng tuần',
          link: 'https://www.vtix.vn/vi/travel/abseiling-va-kham-pha-ho-hang-vem',
          coupon: 'HAPPY5',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã HAPPY5 giảm 5% </strong>',
        },
        {
          image:
            'https://www.vtix.vn/travel/expeditions/kayaking-va-kham-pha-ho-hang-vem/poster.webp',
          title: 'Kayaking và khám phá hồ Hang Vẹm (Tour nửa ngày)',
          address:
            'Hoạt động nổi bật: Trekking, Chèo kayak xuyên lòng núi, khám phá hang động đá vôi',
          fullDate: 'Khởi hành thứ 7, chủ nhật hàng tuần',
          link: 'https://www.vtix.vn/vi/travel/kayaking-va-kham-pha-ho-hang-vem',
          coupon: 'HAPPY5',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã HAPPY5 giảm 5% </strong>',
        },
        {
          image:
            'https://www.vtix.vn/travel/expeditions/hang-vem-expedition/poster.webp',
          title: 'Hang Vẹm Expedition (Tour 2 ngày 1 đêm)',
          address:
            'Hình thức tour: Ghép đoàn (tối thiểu 4 khách) hoặc nhóm riêng (linh hoạt trong tuần)',
          fullDate: 'Khởi hành: 09h30 thứ 7 hàng tuần',
          link: 'https://www.vtix.vn/vi/travel/hang-vem-expedition',
          coupon: 'HAPPY5',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã HAPPY5 giảm 5% </strong>',
        },
        {
          image:
            'https://www.vtix.vn/travel/expeditions/chinh-phuc-cong-troi/poster.webp',
          title: 'Cổng Trời Yên Sơn – Thung Lũng Lân Ty (Lạng Sơn)',
          address:
            'Hình thức tour: Ghép đoàn (tối thiểu 4 khách) hoặc nhóm riêng (linh hoạt trong tuần)',
          fullDate: 'Khởi hành: 06h00 mỗi cuối tuần',
          link: 'https://www.vtix.vn/vi/travel/chinh-phuc-cong-troi-yen-son-thung-lung-lan-ty-2n1d',
          coupon: 'HAPPY5',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã HAPPY5 giảm 5% </strong>',
        },
        {
          image: 'https://www.vtix.vn/workshop/hon-kien/son-mai/poster.webp',
          title: 'Workshop Nha Trang Xưa – Sơn Mài "Chạm Vào Nét Xưa"',
          address:
            'Nhà hàng Nha Trang Xưa: 30 Thái Thông, Vĩnh Thái, Nha Trang, Khánh Hòa',
          fullDate: 'Sáng 9h00 - 11h00 & Chiều 14h30 - 16h30',
          link: 'https://www.vtix.vn/vi/workshop/nha-trang-xua-workshop-son-mai-cham-vao-net-xua',
          coupon: 'AMAZING',
          couponDes:
            '<strong class="color-black">Ưu đãi: Nhập mã AMAZING giảm 5% </strong>',
        },
      ],
      list: [
        {
          image:
            'https://www.vtix.vn/event/madame-show-nhung-duong-chim-bay/poster.webp',
          title: 'MADAME SHOW - NHỮNG ĐƯỜNG CHIM BAY',
          address: 'Số 2 Yết Kiêu - Phường 5 - Tp. Đà Lạt (MADAME DE DALAT)',
          fullDate: '18:30 - 19:30 thứ 7 hàng tuần',
          link: 'https://www.vtix.vn/vi/event/madame-show-nhung-duong-chim-bay',
          coupon: null,
          couponDes: null,
        },
        {
          image:
            'https://www.vtix.vn/workshop/cso/bao-tang-cso-gallery/poster.webp',
          title: 'BẢO TÀNG CSO GALLERY',
          address: '229 Cửa Đại, Phường Cẩm Châu, TP Hội An, Quảng Nam',
          fullDate: 'Từ 8:00-17:00 | Thứ 3-Chủ Nhật',
          link: 'https://www.vtix.vn/vi/workshop/bao-tang-cso-gallery',
          coupon: null,
          couponDes: null,
        },
        {
          image:
            'https://www.vtix.vn/workshop/uniace/lam-chu-ban-than-toan-dien/poster.webp',
          title: 'Làm chủ bản thân toàn diện',
          address: '106 Điện Biên Phủ, Đa Kao, Q1, Hồ Chí Minh',
          fullDate: 'Từ tháng 03 đến tháng 11 năm 2025',
          link: 'https://www.vtix.vn/vi/workshop/lam-chu-ban-than-toan-dien',
          coupon: 'SALE25',
          couponDes:
            '<strong class="color-white">Giảm 25%</strong> <span style="font-weight: 400" class="color-white">áp dụng từ 2 vé trở lên</span>',
        },
        {
          image:
            'https://www.vtix.vn/workshop/moss-frame-ban-hoa-ca-cua-thien-nhien/poster.webp',
          title: 'Workshop Moss Frame - Bản hòa ca của thiên nhiên',
          address:
            'Cửa hàng Cây cảnh mini 9X GARDEN - 479/38 Phan Văn Trị, P.5, Gò Vấp',
          fullDate: 'Thứ 7, Chủ nhật hàng tuần',
          link: 'https://www.vtix.vn/vi/workshop/moss-frame-ban-hoa-ca-cua-thien-nhien',
          coupon: 'SALE25',
          couponDes:
            '<strong class="color-white">Giảm 25%</strong> <span style="font-weight: 400" class="color-white">áp dụng từ 2 vé trở lên</span>',
        },
        {
          image:
            'https://www.vtix.vn/workshop/kokedama-green-in-our-hands/poster.webp',
          title: 'Workshop Kokedama - Green in Our Hands',
          address:
            'Cửa hàng Cây cảnh mini 9X GARDEN - 479/38 Phan Văn Trị, P.5, Gò Vấp',
          fullDate: 'Thứ 7, Chủ nhật hàng tuần',
          link: 'https://www.vtix.vn/vi/workshop/kokedama-green-in-our-hands',
          coupon: null,
          couponDes: null,
        },
        {
          image:
            'https://www.vtix.vn/workshop/terrarium-comback-home/poster.webp',
          title: 'Workshop Terrarium - Comeback Home',
          address:
            'Cửa hàng Cây cảnh mini 9X GARDEN - 479/38 Phan Văn Trị, P.5, Gò Vấp',
          fullDate: 'Thứ 7, Chủ nhật hàng tuần',
          link: 'https://www.vtix.vn/vi/workshop/terrarium-comback-home',
          coupon: null,
          couponDes: null,
        },
      ],
    };

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    const limit = approve === 'true' ? `` : '?limit=1';
    const getAllUserRole = await firstValueFrom(
      this.httpService.get(
        `${app_domain}/_api/users/get-all-user-role${limit}`,
        {
          headers,
        },
      ),
    ).then((data) => {
      return data.data;
    });

    console.info(
      `<<<<<<<<<<<Total email will send: ${getAllUserRole.data.length}>>>>>>>>>>>>>`,
    );
    let countSentSuccess = 0;
    let countSentFail = 0;

    if (getAllUserRole.data && !_.isEmpty(getAllUserRole.data)) {
      for (let i = 0; i < getAllUserRole.data.length; i++) {
        const item = getAllUserRole.data[i];
        const email = approve === 'true' ? item.email : '<EMAIL>'; // '<EMAIL>';

        console.info(
          `<<<<<<<<<<<Begin send to ${email} | STT: ${i + 1} / ${getAllUserRole.data.length} >>>>>>>>>>>>>`,
        );
        await this.sendMail({
          data,
          template: `mail/marketing/${template}`,
          email: email,
          subjectEmail: defineSubjectTitle,
        })
          .then(async () => {
            countSentSuccess++;
            // Dừng lại sau khi gửi mail thành công
            console.info(`<<<<<<<<<<<Sent ${email} finished>>>>>>>>>>>>>`);
            await this.delayTime(this.timeDelayNextZone);
          })
          .catch((error) => {
            countSentFail++;
          });

        // Chỉ dành cho testing
        // approve = false hoặc undefined
        if (!approve) {
          break;
        }
      }
    }

    console.info(
      `<<<<<<<<<<< RESULT: Sent success: ${countSentSuccess} | Sent fail: ${countSentFail} >>>>>>>>>>>>>`,
    );
  }

  @Option({
    flags: '-template, --template <template>',
  })
  parseTemplate(val: string): string {
    return val;
  }

  @Option({
    flags: '-approve, --approve <approve>',
  })
  parseApprove(val: string): string {
    return val;
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }

  // Func gửi email
  async sendMail({ data, template, email, subjectEmail }) {
    try {
      const transporter = nodemailer.createTransport({
        service: config.config_email_marketing.service,
        port: config.config_email_marketing.port,
        host: config.config_email_marketing.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email_marketing.email_user,
          pass: config.config_email_marketing.email_password,
        },
      });
      const templatePath = path.join(
        __dirname,
        '..',
        'views',
        `${template}.ejs`,
      );
      console.log('templatePath > ', templatePath);
      return await ejs.renderFile(
        templatePath,
        data,
        async function (err, html) {
          if (err) {
            console.log('Error rendering template:', err);
            return;
          }
          const mailOptions = {
            from: config.config_email_marketing.email_sender,
            to: email,
            subject: `[VTIX] ${subjectEmail}`,
            html: html,
          };
          return await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
            } else {
              console.log('SEND MAIL SUCCESS TO', email);
            }
          });
        },
      );
    } catch (error) {
      console.log('Send email marketing error > ', error);
    }
  }
}
