import config from '../config/setting';
import Common from '../common/common';
import nodemailer from 'nodemailer';
import moment from 'moment';
import moment_timezone from 'moment-timezone';
import ActivityPdf from './activity_pdf';
import Queue from 'bull';
import fs from 'fs';
import * as path from 'path';
import * as ejs from 'ejs';

const fileNameLog = 'activity_mail';

class ActivityMail {
  static async sendMail(res, template, data, attachment, email) {
    //tạo log header
    Common.logToFile('HEADER-activity-mail', data.cart.email, fileNameLog);
    // send mail
    // log file
    try {
      const attachments = await ActivityPdf.createPdf(res, data);
      const transporter = nodemailer.createTransport({
        service: config.config_email.service,
        port: config.config_email.port,
        host: config.config_email.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email.email_user,
          pass: config.config_email.email_password,
        },
      });
      // const emailQueue = new Queue('order_confirm_emails', { redis: { port: 6379, host: '127.0.0.1' } });
      const templatePath = path.join(
        __dirname,
        '..',
        'views',
        `${template}.ejs`,
      );
      await res.render(
        templatePath,
        { moment: moment, ...data },
        async function (err, html) {
          console.log('EMAIL');
          console.log(err);

          // emailQueue.add({
          //   fromEmail: config.config_email.email_sender,
          //   toEmail: data.cart.email,
          //   subject: 'Order Confirmation',
          //   messageBody: html,
          //   attachments: attachments
          // }, { priority: 3 });

          // if(!err){
          // emailQueue.process(async (job, done) => {
          // job.progress(42);
          // const { fromEmail, toEmail, subject, messageBody, attachments } = job.data;
          // const mailOptions = {
          //   from: fromEmail,
          //   to: toEmail,
          //   subject: subject,
          //   html: messageBody,
          //   attachments: attachments
          // }
          console.log(email ? email : data.userinfo.email);
          const mailOptions = {
            from: config.config_email.email_sender,
            to: email ? email : data.userinfo.email,
            subject: `Order Confirmation / Xác nhận thanh toán cho đơn hàng ${data.paymentDetail.code}`,
            html: html,
            attachments: attachments,
          };
          // console.log(mailOptions);
          // Send email here
          await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
              // //tạo log body
              Common.logToFile('BODY-activity-mail', err, fileNameLog);
            } else {
              // Common.logToFile('forgot_password-success', info, fileNameLog);
              console.log('SEND MAIL SUCCESS ');
              // console.log(info);
            }
          });
          //   done();
          // });
          // }
        },
      );
    } catch (error) {
      console.log('Lỗi11', error);
    }
  }

  static async executeSendEmail(
    attachments: any,
    res,
    template,
    data,
    attachment,
    email,
    send_email,
    notify_email = [],
  ) {
    // Cho phép gửi email hay không?
    if (send_email) {
      const transporter = nodemailer.createTransport({
        service: config.config_email.service,
        port: config.config_email.port,
        host: config.config_email.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email.email_user,
          pass: config.config_email.email_password,
        },
      });
      const templatePath = path.join(
        __dirname,
        '..',
        'views',
        `${template}.ejs`,
      );

      await res.render(
        templatePath,
        {
          moment: moment,
          ...data,
          seat_name: `${data.zone_title && data.zone_title.length > 0 ? `${data.zone_title} / ` : ''}${data.zone_name}-${data.seat_name.length > 1 ? data.seat_name : `0${data.seat_name}`}`,
          show_ticket_seat: data.show_ticket_seat,
        },
        async function (err, html) {
          if (err) {
            console.log('Error rendering template:', err);
            return;
          }
          const mailOptions: any = {
            from: config.config_email.email_sender,
            to: email ? email : data.userinfo.username,
            subject: `Order Confirmation / Xác nhận thanh toán cho đơn hàng ${data.paymentDetail.code}`,
            html: html,
            attachments: attachments,
          };
          // console.log('notify_email :>> ', notify_email);
          // if (notify_email.length > 0) {
          //   mailOptions.bcc = notify_email.join(',');
          // }
          // console.log('mailOptions.bcc :>> ', mailOptions.bcc);
          await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
              // //tạo log body
              Common.logToFile('BODY-activity-mail', err, fileNameLog);
            } else {
              // Common.logToFile('forgot_password-success', info, fileNameLog);
              console.log(
                'SEND MAIL SUCCESS TO',
                email ? email : data.userinfo.username,
              );
              // console.log(info);
            }
          });
        },
      );
    }
  }
  static async sendMailV2(res, template, data, attachment, email, send_email) {
    const payment_detail = data.paymentDetail;
    const event_stage_date = data.event_stage_date;
    const notify_email = event_stage_date.notify_email ?? [];
    let textDateShow = '';
    if (event_stage_date?.is_unlimited && event_stage_date.is_unlimited) {
      textDateShow = 'Vé vô thời hạn / Unlimited ticket';
    } else {
      textDateShow = `${moment(event_stage_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format(`DD/MM/YYYY hh:mm`)} ${moment(event_stage_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format('a')
        .toUpperCase()}`;

      if (!data.show_hour_event) {
        textDateShow = `${moment(event_stage_date.show_date * 1000)
          .tz('Asia/Ho_Chi_Minh')
          .format(`DD/MM/YYYY`)
          .toUpperCase()}`;
      }
    }
    if (
      event_stage_date?.short_desc_vi &&
      event_stage_date.short_desc_vi.length > 0
    ) {
      textDateShow = `${textDateShow}\n${event_stage_date.short_desc_vi}`;
    }
    if (
      event_stage_date?.short_desc_en &&
      event_stage_date.short_desc_en.length > 0
    ) {
      textDateShow = `${textDateShow}\n${event_stage_date.short_desc_en}`;
    }

    if (payment_detail.date_use > 0) {
      textDateShow = `${moment(payment_detail.date_use * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format(`DD/MM/YYYY`)}`;
      if (event_stage_date.attributes.text_dropdown_date) {
        textDateShow = `${textDateShow} | ${event_stage_date.attributes?.text_dropdown_date || ''}`;
      } else if (event_stage_date.attributes.text_show_time) {
        textDateShow = `${textDateShow} | ${event_stage_date.attributes?.text_show_time || ''}`;
      }
    } else {
      if (event_stage_date.attributes.text_show_time) {
        textDateShow = event_stage_date.attributes.text_show_time;
      }
    }

    const convertData = {
      ...data,
      event_stage_date: {
        ...event_stage_date,
        textDateShow: textDateShow,
      },
    };

    try {
      if (send_email) {
        if (
          event_stage_date.attributes &&
          JSON.parse(event_stage_date.attributes?.participants_form ?? false) ==
            true
        ) {
          await ActivityPdf.createPdfForRunner(res, convertData).then(
            async (attachments) => {
              await this.executeSendEmail(
                attachments,
                res,
                template,
                convertData,
                attachment,
                email,
                send_email,
                notify_email,
              ).then(async () => {
                // Logic gửi mail đến người nhận thông tin
                if (
                  notify_email.length > 0 &&
                  process.env.NODE_ENV === 'production'
                ) {
                  for (const email_partner of notify_email) {
                    await this.executeSendEmail(
                      attachments,
                      res,
                      'mail/notify_order_success_vi',
                      convertData,
                      attachment,
                      email_partner,
                      send_email,
                      [],
                    );
                  }
                }
              });
            },
          );
        } else if (
          event_stage_date.attributes &&
          JSON.parse(event_stage_date.attributes?.is_voucher_ticket ?? false) ==
            true
        ) {
          await ActivityPdf.createPdfForVoucher(res, convertData).then(
            async (attachments) => {
              await this.executeSendEmail(
                attachments,
                res,
                'mail/booking_voucher_success',
                convertData,
                attachment,
                email,
                send_email,
                notify_email,
              );
            },
          );
        } else {
          await ActivityPdf.createPdfV2(res, convertData).then(
            async (attachments) => {
              await this.executeSendEmail(
                attachments,
                res,
                template,
                convertData,
                attachment,
                email,
                send_email,
                notify_email,
              ).then(async () => {
                // Logic gửi mail đến người nhận thông tin
                console.log('>>>>>>>>>>>>> notify_email :>> ', notify_email);
                if (
                  notify_email.length > 0 &&
                  process.env.NODE_ENV === 'production'
                ) {
                  for (const email_partner of notify_email) {
                    await this.executeSendEmail(
                      attachments,
                      res,
                      'mail/notify_order_success_vi',
                      convertData,
                      attachment,
                      email_partner,
                      send_email,
                      [],
                    );
                  }
                }
              });
            },
          );
        }
      } else {
        // TODO:
        // await ActivityPdf.createPdfV3(res, convertData).then(
        //   async (attachments) => {
        //     await this.executeSendEmail(
        //       attachments,
        //       res,
        //       template,
        //       convertData,
        //       attachment,
        //       email,
        //       send_email,
        //     );
        //   },
        // );
      }
    } catch (error) {
      console.log('Lỗi11', error);
    }
  }

  static async sendMailTest(res, template, data, attachment) {
    //tạo log header
    Common.logToFile('HEADER-activity-mail', data.cart.email, fileNameLog);
    // send mail
    // log file
    try {
      const attachments = await ActivityPdf.createPdf(res, data);
      // const emailQueue = new Queue('order_confirm_emails', { redis: { port: 6379, host: '127.0.0.1' } });

      const transporter = nodemailer.createTransport({
        service: config.config_email_test.service,
        port: config.config_email_test.port,
        host: config.config_email_test.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email_test.email_user,
          pass: config.config_email_test.email_password,
        },
      });
      await res.render(
        template,
        { moment: moment, ...data },
        async function (err, html) {
          console.log('EMAIL');
          console.log(err);

          // emailQueue.add({
          //   fromEmail: config.config_email_test.email_sender,
          //   toEmail: data.cart.email,
          //   subject: 'Order Confirmation',
          //   messageBody: html,
          //   attachments: attachments
          // }, { priority: 3 });

          // if(!err){
          // emailQueue.process(async (job, done) => {
          // job.progress(42);
          // const { fromEmail, toEmail, subject, messageBody, attachments } = job.data;
          // const mailOptions = {
          //   from: fromEmail,
          //   to: toEmail,
          //   subject: subject,
          //   html: messageBody,
          //   attachments: attachments
          // }
          const mailOptions = {
            from: config.config_email_test.email_sender,
            to: data.cart.email,
            subject: `Order Confirmation / Xác nhận thanh toán cho đơn hàng ${data.paymentDetail.code}`,
            html: html,
            attachments: attachments,
          };
          // console.log(mailOptions);
          // Send email here
          await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
              // //tạo log body
              Common.logToFile('BODY-activity-mail', err, fileNameLog);
            } else {
              console.log('Message sent! Message ID: ', info);
              Common.logToFile('Message sent! Message ID:', info, fileNameLog);
              console.log('SEND MAIL SUCCESS ');
              // console.log(info);
            }
          });
          //   done();
          // });
          // }
        },
      );
    } catch (error) {
      console.log('Lỗi11', error);
    }
  }

  static async sendMailLahome(res, template, data, attachment) {
    //tạo log header
    Common.logToFile('HEADER-activity-mail', data.cart.email, fileNameLog);
    // send mail
    // log file
    try {
      const attachments = await ActivityPdf.createPdfLahome(res, data);

      const transporter = nodemailer.createTransport({
        service: config.config_email.service,
        port: config.config_email.port,
        host: config.config_email.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email.email_user,
          pass: config.config_email.email_password,
        },
      });
      // const emailQueue = new Queue('order_confirm_emails', { redis: { port: 6379, host: '127.0.0.1' } });

      await res.render(
        template,
        { moment: moment, ...data },
        async function (err, html) {
          console.log('EMAIL');
          console.log(err);

          // emailQueue.add({
          //   fromEmail: config.config_email.email_sender,
          //   toEmail: data.cart.email,
          //   subject: 'Order Confirmation',
          //   messageBody: html,
          //   attachments: attachments
          // }, { priority: 3 });

          // if(!err){
          // emailQueue.process(async (job, done) => {
          // job.progress(42);
          // const { fromEmail, toEmail, subject, messageBody, attachments } = job.data;
          // const mailOptions = {
          //   from: fromEmail,
          //   to: toEmail,
          //   subject: subject,
          //   html: messageBody,
          //   attachments: attachments
          // }
          const mailOptions = {
            from: config.config_email.email_sender,
            to: data.cart.email,
            subject: 'Thư mời tham dự Lễ ký kết',
            html: html,
            attachments: attachments,
          };
          // console.log(mailOptions);
          // Send email here
          await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
              console.log('EMAIL: ', data.cart.email);
              // //tạo log body
              Common.logToFile('BODY-activity-mail', err, fileNameLog);
            } else {
              // Common.logToFile('forgot_password-success', info, fileNameLog);
              console.log('SEND MAIL SUCCESS ');
              // console.log(info);
            }
          });
          //   done();
          // });
          // }
        },
      );
    } catch (error) {
      console.log('Lỗi11', error);
    }
  }

  static async sendMailTickets(res, template, data, attachment) {
    //tạo log header
    Common.logToFile(
      'HEADER-activity-mail-tickets',
      data.cart.email,
      fileNameLog,
    );
    // send mail
    // log file

    const attachments = await ActivityPdf.createPdfQR(res, data);
    console.log(attachments);

    const transporter = nodemailer.createTransport({
      service: config.config_email.service,
      port: config.config_email.port,
      host: config.config_email.host,
      secure: true,
      pool: true,
      auth: {
        user: config.config_email.email_user,
        pass: config.config_email.email_password,
      },
    });
    const emailQueue = new Queue('tickets_emails', {
      redis: { port: 6379, host: '127.0.0.1' },
    });

    res.render(template, { moment: moment, ...data }, function (err, html) {
      emailQueue.add(
        {
          fromEmail: config.config_email.email_sender,
          toEmail: data.cart.email,
          subject: 'Tickets Information',
          messageBody: html,
          attachments: attachments,
        },
        { priority: 8 },
      );

      // if(!err){
      emailQueue.process(async (job, done) => {
        // job.progress(42);
        const { fromEmail, toEmail, subject, messageBody, attachments } =
          job.data;
        const mailOptions = {
          from: fromEmail,
          to: toEmail,
          subject: subject,
          html: messageBody,
          attachments: attachments,
        };
        console.log(mailOptions);
        // Send email here
        await transporter.sendMail(mailOptions, function (err, info) {
          if (err) {
            console.log('SEND MAIL FAIL');
            console.log(err);
            // //tạo log body
            Common.logToFile('BODY-activity-mail', err, fileNameLog);
          } else {
            // Common.logToFile('forgot_password-success', info, fileNameLog);
            console.log('SEND MAIL SUCCESS ');
            console.log(info);
          }
        });
        done();
      });

      // }
    });
  }

  static async sendMailForgot(res, template, data) {
    const transporter = nodemailer.createTransport({
      service: config.config_email.service,
      port: config.config_email.port,
      host: config.config_email.host,
      secure: true,
      pool: true,
      auth: {
        user: config.config_email.email_user,
        pass: config.config_email.email_password,
      },
    });
    // const emailQueue = new Queue('tickets_emails', { redis: { port: 6379, host: '127.0.0.1' } });
    const templatePath = path.join(__dirname, '..', 'views', `${template}.ejs`);
    await ejs.renderFile(
      templatePath,
      { moment: moment, ...data },
      async (err, html) => {
        if (!err) {
          const mailOptions = {
            from: config.config_email.email_sender,
            to: data.email,
            subject: 'Reset Password',
            html: html,
          };

          await transporter.sendMail(mailOptions, (err, info) => {
            if (err) {
              console.log('SEND MAIL FAIL', err);
              res.status(500).send({
                status: 500,
                message: 'Failed to send email',
                error: err,
              });
            } else {
              console.log('SEND MAIL SUCCESS');
              res
                .status(200)
                .send({ status: 200, message: 'Email sent successfully' });
            }
          });
        } else {
          console.error('Error rendering template:', err);
          res
            .status(500)
            .send({ status: 500, message: 'Failed to send email', error: err });
        }
      },
    );
  }
}

export default ActivityMail;
