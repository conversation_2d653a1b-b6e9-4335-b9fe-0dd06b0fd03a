import {
  Table,
  Column,
  Model,
  PrimaryKey,
  DataType,
  Default,
  ForeignKey,
} from 'sequelize-typescript';

@Table({
  tableName: 'participants_info',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class ParticipantInfo extends Model<ParticipantInfo> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID của table temp_seats',
  })
  temp_seats_id: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID của table teamp_quantities',
  })
  temp_quantities_id: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: 'ID của transaction',
  })
  transaction_id: string;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
    comment: '<PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h của người tham gia sự kiện',
  })
  info: object;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  status: boolean;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  created_at: Date;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;
}
