{"containerDefinitions": [{"name": "worker", "image": "056547273288.dkr.ecr.ap-southeast-1.amazonaws.com/production-worker:latest", "cpu": 256, "memory": 500, "portMappings": [{"containerPort": 80, "protocol": "tcp", "hostPort": 0}], "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/production/", "awslogs-create-group": "true", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "worker"}}}], "family": "production-worker", "executionRoleArn": "arn:aws:iam::056547273288:role/vtix-production-ecs-task-execute", "networkMode": "bridge"}