import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Event } from '@events/models/event.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';

@Table({
  tableName: 'event_stage',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class EventStage extends Model<EventStage> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  poster_stage_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  poster_stage_en: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '1: Zonemap | 2: custom ticket',
  })
  stage_type: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Địa điểm - VI',
  })
  venue_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'mo ta gia - VI',
  })
  ticket_price_desc_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'mo ta gia - EN',
  })
  ticket_price_desc_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Địa điểm - EN',
  })
  venue_en: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  updated_at: Date;

  // Associations
  @BelongsTo(() => Event, { foreignKey: 'event_id', as: 'event' })
  event: Event;

  @HasMany(() => EventStageDate, {
    foreignKey: 'event_stage_id',
    as: 'event_stage_date',
  })
  event_stage_date: EventStageDate[];

  @HasMany(() => EventZoneDetail, {
    foreignKey: 'event_stage_id',
    as: 'event_zone_detail',
  })
  event_zone_detail: EventZoneDetail[];
}
