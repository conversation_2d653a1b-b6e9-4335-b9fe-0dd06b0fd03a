import {
  CanActivate,
  ExecutionContext,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class InternalAPIGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const internalKey = request.headers['x-hash-key'];
    if (internalKey === process.env.INTERNAL_X_KEY) {
      return true;
    } else {
      console.log('Invalid key');
      throw new NotFoundException('Invalid key');
    }
  }
}
