import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { SchedulerRegistry } from '@nestjs/schedule';
import { UserHasVoucher } from './models/user_has_voucher.model';
import { CreationAttributes } from 'sequelize';
import { Voucher } from 'core/vouchers/models/voucher.model';
import { VoucherProvider } from '@voucher_providers/models/voucher_provider.model';
import { User } from 'core/users/models/user.model';
import config from 'config/setting';
import nodemailer from 'nodemailer';
import path from 'path';
import * as ejs from 'ejs';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import { Sequelize } from 'sequelize-typescript';
import { Op } from 'sequelize';
import { UserBookingLog } from 'core/user_booking_log/models/user_booking_log.model';
import _ from 'lodash';
import moment from 'moment';

export enum VoucherCondition {
  HAS_PURCHASED_TICKET = 'has_purchased_ticket',
  PAYMENT_FAIL = 'payment_fail',
  BUY_STEP_1 = 'buy_step_1',
  BUY_STEP_2 = 'buy_step_2',
  BUY_STEP_3 = 'buy_step_3',
}

@Injectable()
export class UserHasVoucherService {
  constructor(
    @InjectModel(UserHasVoucher)
    private readonly userHasVoucherModel: typeof UserHasVoucher,
    @InjectModel(Voucher)
    private readonly voucherModel: typeof Voucher,
    @InjectModel(VoucherProvider)
    private readonly voucherProviderModel: typeof VoucherProvider,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(PaymentDetail)
    private readonly paymentDetailModel: typeof PaymentDetail,
    @InjectModel(UserBookingLog)
    private readonly userBookingLogModel: typeof UserBookingLog,

    private readonly schedulerRegistry: SchedulerRegistry,
    private sequelize: Sequelize,
  ) { }

  async getListUserHasVoucher(conditions: any, includes: boolean = false) {
    const { user, page = 1, limit = 10, ...rest } = conditions;
    const offset = (page - 1) * limit;
    if (!user.is_admin) {
      rest.user_id = user.id;
    }

    const args = {
      where: {
        ...rest,
      },
      offset,
      limit,
      raw: true,
      nest: true,
    };
    if (includes) {
      args['include'] = [
        {
          model: Voucher,
          as: 'voucher',
          attributes: [
            'id',
            'name',
            'description',
            'code',
            'photo',
            'discount_value',
            'discount_type',
            'from_date',
            'to_date',
            'status',
            'attributes',
          ],
          required: false,
          include: [
            {
              model: VoucherProvider,
              as: 'voucher_provider',
              attributes: ['id', 'provider_name', 'provider_description'],
            },
          ],
        },
      ];
    }

    const [result, total] = await Promise.all([
      this.userHasVoucherModel.findAll(args),
      this.userHasVoucherModel.count({
        where: {
          ...rest,
        },
      }),
    ]);

    const converData = result.map((item) => {
      const { voucher_provider = null, status, ...voucherData } = item.voucher;
      return {
        ...voucherData,
        provider_id: voucher_provider?.id,
        provider_name: voucher_provider?.provider_name,
        provider_description: voucher_provider?.provider_description,
        voucher_status: item.status,
        used_at: item.used_at,
        created: item.created_at,
      };
    });
    return {
      list: converData,
      total,
      limit: parseInt(limit),
      page: parseInt(page),
    };
  }

  async getUserHasVoucherDetailByVoucherId(
    voucher_id: string,
    user_id: string,
  ) {
    return await this.userHasVoucherModel.findOne({
      where: {
        voucher_id,
        user_id,
      },
      include: [
        {
          model: Voucher,
          as: 'voucher',
          attributes: [
            'id',
            'name',
            'description',
            'code',
            'photo',
            'discount_value',
            'discount_type',
            'from_date',
            'to_date',
            'status',
          ],
          required: false,
        },
      ],
      raw: true, // <----------- Magic is here
    });
  }

  async assignVoucher(payload: any) {
    const { voucher_id, user_id } = payload;
    const voucher = await this.voucherModel.findOne({
      where: {
        id: voucher_id,
        status: true,
      },
    });
    if (!voucher) {
      throw new Error('Voucher not found');
    }
    const userHasVoucher = await this.userHasVoucherModel.findOne({
      where: {
        voucher_id,
        user_id,
      },
    });
    if (userHasVoucher) {
      throw new Error('Voucher already assigned');
    }
    const userHasVoucherCreated = await this.userHasVoucherModel.create(
      payload as CreationAttributes<UserHasVoucher>,
    );
    await this.scheduleVoucherEmails([userHasVoucherCreated]);
    return userHasVoucherCreated;
  }

  async bulkAssignVoucher(payload: any) {
    const { user_ids, voucher_provider_id, status } = payload;
    const voucherProvider = await this.voucherProviderModel.findOne({
      where: {
        id: voucher_provider_id,
      },
    });
    if (!voucherProvider) {
      throw new Error('Voucher provider not found');
    }
    const vouchers = await this.voucherModel.findAll({
      where: {
        voucher_provider_id,
        status: true,
      },
    });
    if (!vouchers) {
      throw new Error('Voucher not found');
    }
    // Get all existing user-voucher combinations
    const existingAssignments = await this.userHasVoucherModel.findAll({
      attributes: ['voucher_id', 'user_id'],
      where: {
        voucher_id: vouchers.map((v) => v.id),
        user_id: user_ids,
      },
      raw: true,
    });

    const existingCombinations = new Set(
      existingAssignments.map(
        (assignment) => `${assignment.voucher_id}-${assignment.user_id}`,
      ),
    );

    const voucherIds = vouchers.map((voucher) => voucher.id);
    const dataInsert = user_ids
      .map((user_id, index) => {
        const combination = `${voucherIds[index]}-${user_id}`;
        if (!existingCombinations.has(combination)) {
          return {
            voucher_id: voucherIds[index],
            user_id,
            status: status || 'active',
          };
        }
        return null;
      })
      .filter(Boolean);

    await this.scheduleVoucherEmails(dataInsert);
    return await this.bulkCreate(dataInsert);
  }

  async bulkAssignVoucherCondition(payload: any) {
    const { voucher_provider_id, status, condition } = payload;
    const voucherProvider = await this.voucherProviderModel.findOne({
      where: {
        id: voucher_provider_id,
      },
    });
    if (!voucherProvider) {
      throw new Error('Voucher provider not found');
    }
    const vouchers = await this.voucherModel.findAll({
      where: {
        voucher_provider_id,
        status: true,
      },
      raw: true,
    });
    if (!vouchers) {
      throw new Error('Voucher not found');
    }

    // Get all voucher IDs that have been assigned to users
    const assignedVouchers = new Set(
      (
        await this.userHasVoucherModel.findAll({
          attributes: ['voucher_id'],
          where: {
            voucher_id: vouchers.map((v) => v.id),
            status: 'active',
          },
          raw: true,
        })
      ).map((v) => v.voucher_id),
    );
    // Filter to get only unassigned vouchers
    const unassignedVouchers = vouchers.filter(
      (voucher) => !assignedVouchers.has(voucher.id),
    );
    const unassignedVouchersLength = unassignedVouchers.length;
    if (unassignedVouchersLength === 0) {
      throw new Error('No unassigned vouchers available');
    }

    let dataInsert: Array<{
      voucher_id: string;
      user_id: string;
      status: 'active' | 'used' | 'expired';
    }> = [];
    switch (condition) {
      case VoucherCondition.HAS_PURCHASED_TICKET:
        const userHasPurchaseTicket = await this.getUserHasPurchaseTicket(
          unassignedVouchersLength,
        );
        dataInsert = userHasPurchaseTicket.map((user_id, index) => {
          return {
            voucher_id: unassignedVouchers[index].id,
            user_id,
            status: status || 'active',
          };
        });
        break;
      case VoucherCondition.PAYMENT_FAIL:
        const userHasPaymentFail = await this.getUserHasPaymentFail(
          unassignedVouchersLength,
        );
        dataInsert = userHasPaymentFail.map((user_id, index) => {
          return {
            voucher_id: unassignedVouchers[index].id,
            user_id,
            status: status || 'active',
          };
        });
        break;
      case VoucherCondition.BUY_STEP_1:
        const userHasBuyStep1 = await this.getUserHasBuyStep(
          unassignedVouchersLength,
          1,
        );
        dataInsert = userHasBuyStep1.map((user_id, index) => {
          return {
            voucher_id: unassignedVouchers[index].id,
            user_id,
            status: status || 'active',
          };
        });
        break;
      case VoucherCondition.BUY_STEP_2:
        const userHasBuyStep2 = await this.getUserHasBuyStep(
          unassignedVouchersLength,
          2,
        );
        dataInsert = userHasBuyStep2.map((user_id, index) => {
          return {
            voucher_id: unassignedVouchers[index].id,
            user_id,
            status: status || 'active',
          };
        });
        break;
      case VoucherCondition.BUY_STEP_3:
        const userHasBuyStep3 = await this.getUserHasBuyStep(
          unassignedVouchersLength,
          3,
        );
        dataInsert = userHasBuyStep3.map((user_id, index) => {
          return {
            voucher_id: unassignedVouchers[index].id,
            user_id,
            status: status || 'active',
          };
        });
        break;
      default:
        break;
    }
    console.log('dataInsert :>> ', dataInsert);
    await this.scheduleVoucherEmails(dataInsert);
    return await this.bulkCreate(dataInsert);
  }

  private async getUserHasPurchaseTicket(limit: number): Promise<string[]> {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    const paidUsers = await this.paymentDetailModel.findAll({
      attributes: ['user_id'],
      where: {
        status_payment: true,
        created_at: {
          [Op.gte]: threeMonthsAgo,
        },
      },
      group: ['user_id'],
      order: this.sequelize.literal('RANDOM()'),
      limit: limit,
      raw: true,
    });

    let userIds = paidUsers.map((user) => user.user_id);
    const remainingLimit = limit - userIds.length;
    if (remainingLimit > 0) {
      const remainingUsers = await this.getUserHasPaymentFail(remainingLimit);
      const uniqueUserIds = new Set([...userIds, ...remainingUsers]);
      userIds = Array.from(uniqueUserIds).slice(0, limit);
    }
    return userIds;
  }

  private async getUserHasPaymentFail(limit: number): Promise<string[]> {
    const unpaidUsers = await this.paymentDetailModel.findAll({
      attributes: ['user_id'],
      where: {
        status_payment: false,
      },
      group: ['user_id'],
      order: this.sequelize.literal('RANDOM()'),
      limit: limit,
      raw: true,
    });
    let userIds = unpaidUsers.map((user) => user.user_id);
    const remainingLimit = limit - userIds.length;
    if (remainingLimit > 0) {
      const remainingUsers = await this.getListUserIds(remainingLimit, userIds);
      const uniqueUserIds = new Set([...userIds, ...remainingUsers]);
      userIds = Array.from(uniqueUserIds).slice(0, limit);
    }
    return userIds;
  }

  private async getUserHasBuyStep(
    limit: number,
    step: number,
  ): Promise<string[]> {
    // const monthsAgo = new Date();
    // monthsAgo.setMonth(monthsAgo.getMonth() - 3);
    const users = await this.userBookingLogModel.findAll({
      attributes: ['user_id'],
      where: {
        step,
        // created_at: {
        //   [Op.gte]: monthsAgo,
        // },
      },
      group: ['user_id'],
      order: this.sequelize.literal('RANDOM()'),
      limit: limit,
      raw: true,
    });
    let userIds = users.map((user) => user.user_id);
    const remainingLimit = limit - userIds.length;
    if (remainingLimit > 0) {
      const remainingUsers = await this.getUserHasPaymentFail(remainingLimit);
      const uniqueUserIds = new Set([...userIds, ...remainingUsers]);
      userIds = Array.from(uniqueUserIds).slice(0, limit);
    }
    return userIds;
  }

  private async getListUserIds(
    limit: number,
    user_ids: string[],
  ): Promise<string[]> {
    const userIds = await this.userModel.findAll({
      attributes: ['id'],
      where: {
        id: {
          [Op.notIn]: user_ids,
        },
        active: true,
      },
      order: this.sequelize.literal('RANDOM()'),
      limit: limit,
      raw: true,
    });
    return userIds.map((user) => user.id);
  }

  async bulkCreate(dataInsert: any[]) {
    return await this.userHasVoucherModel.bulkCreate(dataInsert);
  }

  async removeUserHasVoucher(conditions: any): Promise<any> {
    try {
      await this.userHasVoucherModel.destroy({
        where: {
          ...conditions,
        },
      });
    } catch (error) {
      console.log('error :>> ', error.message);
    }
  }

  async updateStatusUserHasVoucher(
    id: string,
    payload: any,
  ) {
    const dataUpdate: any = {};
    if (payload.status) {
      dataUpdate.status = payload.status;
    }
    if (payload.used_at) {
      dataUpdate.used_at = new Date(payload.used_at);
    }
    return await this.userHasVoucherModel.update(dataUpdate, { where: { id } });
  }

  private async scheduleVoucherEmails(assignments: any[]) {
    for (const assignment of assignments) {
      const { voucher_id, user_id } = assignment;
      const voucher: any = await this.voucherModel.findOne({
        where: { id: voucher_id },
        include: [
          {
            model: VoucherProvider,
            as: 'voucher_provider',
            attributes: ['provider_name'],
          },
        ],
      });

      if (!voucher) {
        console.error(`Voucher not found for id: ${voucher_id}`);
        continue;
      }

      const user = await this.userModel.findOne({
        where: { id: user_id },
        attributes: ['id', 'name', 'email'],
      });
      if (!user) {
        console.error(`User not found for id: ${user_id}`);
        continue;
      }

      const jobName = `voucher-email-${voucher_id}-${user_id}`;

      // Clear existing timeout if it exists
      try {
        if (this.schedulerRegistry.doesExist('timeout', jobName)) {
          this.schedulerRegistry.deleteTimeout(jobName);
        }
      } catch (e) {
        // Ignore error if timeout doesn't exist
      }

      const timeout = setTimeout(async () => {
        try {
          const user_name = user.name;
          const voucher_provider_name = voucher.voucher_provider.provider_name;
          const voucher_code = voucher.code;
          const voucher_name = voucher.name;
          const voucher_description = voucher.description;
          const formatDiscountValue = (value: string | number): string => {
            const num =
              typeof value === 'number' ? value : parseFloat(value) || 0;
            return voucher.discount_type === 'percentage'
              ? `${num.toFixed(2).replace(/\.?0+$/, '')}%`
              : `${Math.floor(num).toLocaleString('vi-VN')} VND`;
          };

          const voucher_discount_value = !_.isNil(
            voucher.attributes.text_discount || null,
          )
            ? voucher.attributes.text_discount
            : formatDiscountValue(voucher.discount_value);

          let voucher_expired =
            voucher.from_date && voucher.to_date
              ? voucher.from_date == voucher.to_date
                ? `${moment.unix(voucher.from_date).format('DD/MM/YYYY')}`
                : `${moment.unix(voucher.from_date).format('DD/MM/YYYY')} - ${moment.unix(voucher.to_date).format('DD/MM/YYYY')}`
              : 'Voucher không có hạn sử dụng';
          if (
            !_.isNil(voucher.attributes.is_range_use || null) &&
            voucher.attributes.is_range_use &&
            !_.isNil(voucher.attributes.range_date_type || null) &&
            !_.isNil(voucher.attributes.range_number || null)
          ) {
            const rangeDateType = voucher.attributes.range_date_type;
            voucher_expired = moment()
              .add(
                parseInt(voucher.attributes.range_number),
                rangeDateType === 'day'
                  ? 'days'
                  : rangeDateType === 'month'
                    ? 'months'
                    : 'years',
              )
              .format('DD/MM/YYYY');
          }

          const voucher_photo = `https://media.vtix.vn/vouchers/${voucher.photo}`;
          await this.sendMailVoucher({
            data: {
              user_name,
              voucher_provider_name,
              voucher_code,
              voucher_discount_value,
              voucher_expired,
              voucher_name,
              voucher_description,
              my_items:
                process.env.NODE_ENV === 'production'
                  ? 'https://vtix.vn/vi/account/my-items'
                  : 'https://staging.vtix.vn/vi/account/my-items',
            },
            template: `mail/voucher/${!_.isEmpty(voucher.photo) ? 'voucher_attach' : 'voucher_code'}`,
            email: user.email,
            subjectEmail: `⚡️ ƯU ĐÃI ${voucher_discount_value} TỪ ${voucher_provider_name.toUpperCase()} - ${voucher_name}`,
            attachments: !_.isEmpty(voucher.photo)
              ? [
                {
                  filename: `${voucher_provider_name.replace(/\s/g, '_').toLowerCase()}_${moment(voucher.created_at).format('YYYYMMDD_HHmmss')}${path.extname(voucher.photo)}`,
                  href: voucher_photo,
                },
              ]
              : [],
          });
        } catch (error) {
          console.error(`Failed to send voucher email for ${user_id}:`, error);
        }
      }, 5000); // 5 seconds delay

      this.schedulerRegistry.addTimeout(jobName, timeout);
    }
  }

  async sendMailVoucher({
    data,
    template,
    email,
    subjectEmail,
    attachments = [],
  }: {
    data: any;
    template: string;
    email: string;
    subjectEmail: string;
    attachments?: Array<{ filename: string; href: string }>;
  }) {
    const maxRetries = 3;
    let retryCount = 0;
    let success = false;

    while (retryCount < maxRetries && !success) {
      try {
        const transporter = nodemailer.createTransport({
          service: config.config_email.service,
          port: config.config_email.port,
          host: config.config_email.host,
          secure: true,
          pool: true,
          auth: {
            user: config.config_email.email_user,
            pass: config.config_email.email_password,
          },
        });

        const templatePath = path.join(
          __dirname,
          '..',
          '..',
          'views',
          `${template}.ejs`,
        );

        console.log('============ templatePath Email voucher > ', templatePath);

        return await ejs.renderFile(
          templatePath,
          data,
          async function (err, html) {
            if (err) {
              console.log('============ SEND MAIL FAIL');
              console.log(err);
              throw err; // Re-throw to trigger retry
            } else {
              console.log('============  SEND MAIL SUCCESS TO', email);
            }

            const mailOptions = {
              from: config.config_email.email_sender,
              to: email,
              subject: `${subjectEmail}`,
              html: html,
              attachments: attachments,
            };

            return await transporter.sendMail(mailOptions, function (err, info) {
              if (err) {
                console.log('============ SEND MAIL FAIL');
                console.log(err);
                throw err; // Re-throw to trigger retry
              } else {
                console.log('============  SEND MAIL SUCCESS TO', email);
              }
            });
          },
        );

        success = true;
        console.log(`Successfully sent voucher email to ${email} after ${retryCount + 1} attempts`);

      } catch (error) {
        retryCount++;
        console.error(`Error sending voucher email to ${email} (attempt ${retryCount}/${maxRetries}):`, error.message);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff: 2^retryCount seconds)
          const waitTime = Math.pow(2, retryCount) * 1000;
          console.log(`Retrying voucher email to ${email} in ${waitTime / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        } else {
          console.error(`Failed to send voucher email to ${email} after ${maxRetries} attempts`);
        }
      }
    }
  }
}
