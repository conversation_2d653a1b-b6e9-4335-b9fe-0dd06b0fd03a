import * as process from 'process';

const Constants = {
  TITLE_ADMIN: '#',
  COMPANY_NAME: '#',
  IS_WRITE_LOG: true,
  DEFAULT_PASSWORD:
    '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92', //123456
  DISPLAY_FORMAT_DATE: 'DD/MM/YYYY',
  DB_FORMAT_DATE: 'YYYY-MM-DD',
  DB_FORMAT_DATE_TIME: 'YYYY-MM-DD HH:mm:ss',
  DISPLAY_FORMAT_DATE_TIME_EXCEL: 'HH:mm DD-MM-YYYY',
  DISPLAY_FORMAT_DATETIME_SS: 'DD/MM/YYYY HH:mm:ss',
  SERVER_FORMAT_DATETIME_SS: 'YYYY/MM/DD HH:mm:ss',
  FORMAT_IMPORT_SALE_TIME: 'DD/MM/YYYY HH:mm:ss',
  FORMAT_DATE_DISPLAY_CLIENT: 'HH:mm DD/MM/YYYY',
  FORMAT_DATE_PARTNER: 'YYYYMMDDHHmmss',
  VIEWS_DIR: process.cwd() + '/views',
  DEFAULT_TIMEZONE: 'Asia/Ho_Chi_Minh',
  YEAR_MONTH_DATE_HOUR_SECOND: 'YYYY/MM/DD HH:mm:ss',
  YEAR_MONTH_DATE_HOUR_MINUTES: 'YYYY/MM/DD HH:mm',
  DAY_OF_WEEK_VI: 'dddd, Do MMMM YYYY',
  DAY_OF_WEEK_EN: 'ddd, MMM Do YYYY',
  ONLY_DAY_OF_WEEK_VI: 'dddd,',
  ONLY_DAY_OF_WEEK_EN: 'ddd,',
  WITHOUT_DAY_OF_WEEK_VI: 'Do MMMM YYYY',
  WITHOUT_DAY_OF_WEEK_EN: 'MMM Do YYYY',
  DB_TIMEZONE: 'UTC',
  //thêm chuỗi #repl# cho thành string vì khi for nếu ko string thì sẽ bắt đầu từ 0=> chủ nhật
  CONTENT_REQUIRED_FORM: '(*) Nội dung bắt buộc',
  MINETYPE_ALLOW_UPLOAD: {
    'application/msword': ['doc', 'dot'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      'docx',
    'application/vnd.ms-word.document.macroEnabled.12': 'docm',
    'application/vnd.ms-word.template.macroEnabled.12': 'dotm',
    'application/vnd.ms-excel': ['xls', 'xlt', 'xla'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template':
      'xltx',
    'application/vnd.ms-excel.sheet.macroEnabled.12': 'xlsm',
    'application/vnd.ms-excel.template.macroEnabled.12': 'xltm',
    'application/vnd.ms-excel.addin.macroEnabled.12': 'xlam',
    'application/vnd.ms-excel.sheet.binary.macroEnabled.12': 'xlsb',
    'application/vnd.ms-powerpoint': ['ppt', 'pot', 'pps', 'ppa'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      'pptx',
    'application/vnd.openxmlformats-officedocument.presentationml.template':
      'potx',
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow':
      'ppsx',
    'application/vnd.ms-powerpoint.addin.macroEnabled.12': 'ppam',
    'application/vnd.ms-powerpoint.presentation.macroEnabled.12': 'pptm',
    'application/vnd.ms-powerpoint.template.macroEnabled.12': 'potm',
    'application/vnd.ms-powerpoint.slideshow.macroEnabled.12': 'ppsm',
    //mine type pdf
    'application/pdf': 'pdf',
    'application/octet-stream': 'pdf',
    //mine type image
    'image/apng': 'apng',
    'image/bmp': 'bmp',
    'image/gif': 'gif',
    'image/x-icon': ['ico', 'cur'],
    'image/jpeg': ['jpg', 'jfif', 'pjpeg', 'pjp'],
    'image/png': 'png',
    'image/svg+xml': 'svg',
    'image/tiff': ['tif', 'tiff'],
    'image/webp': 'webp',
    'image/*': 'jpgchecksao', //check thêm type có *
  }, //MineType cho phép upload trong server
  CONFIG_SEND_EMAIL: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: '<EMAIL>', // generated ethereal user
      pass: 'vtix.vn', // generated ethereal password
    },
  },
  DIR_UPLOAD: 'upload',
  DIR_UPLOAD_LOG: 'log',
  DIR_IMAGES_PRODUCT: 'images/products',
  DIR_ECODE: 'file/Ecode', //cateAllow
  CATE_ALLOW_MAP: {
    mobifone: 'MOBI',
    viettel: 'VTEL',
    vinaphone: 'VINA',
  },
  //api felix
  STATUS_CODE_SUCCESS_MOMO: '0',
  STATUS_CODE_SUCCESS_PAYOO: 1,
  MSG: {
    SERVER_ERR: 'SERVER_ERR',
    NOT_FOUND_ERR: 'NOT_FOUND_ERR',
    INVALID_TOKEN_ERR: 'INVALID_TOKEN_ERR',
    INVALID_USERNAME_OR_PASSWORD_ERR: 'INVALID_USERNAME_OR_PASSWORD_ERR',
    MISMATCH_PARAMS_ERR: 'MISMATCH_PARAMS_ERR',
    API_NOT_SUPPORT_ERR: 'API_NOT_SUPPORT_ERR',
    CHECKOUT_ERR: 'CHECKOUT_ERR',
    CARD_NOT_ALLOW_ERR: 'CARD_NOT_ALLOW_ERR',
    API_MCARD_PARTNER_NOT_SUPPORT: 'API_MCARD_PARTNER_NOT_SUPPORT',
    ERROR_MCRAD_ERR: 'ERROR_MCRAD_ERR',
    CARD_VALUE_INVALID_ERR: 'CARD_VALUE_INVALID_ERR',
    CATE_MCARD_NOT_EXIST: 'CATE_MCARD_NOT_EXIST',
    INVALID_NUMBER_CARD_ERR: 'INVALID_NUMBER_CARD_ERR',
    ACCOUNT_IS_EXISTED_ERR: 'ACCOUNT_IS_EXISTED_ERR',
    EMAIL_IS_EXISTED_ERR: 'EMAIL_IS_EXISTED_ERR',
    RESTAURANT_NOT_FOUND_ERR: 'RESTAURANT_NOT_FOUND_ERR',
    WRONG_CAPTCHA: 'WRONG_CAPTCHA_ERR',
    CAPTCHA_IS_EXPIRED: 'CAPTCHA_IS_EXPIRED_ERR',
    ACCOUNT_IS_NOT_EXISTED_ERR: 'ACCOUNT_IS_NOT_EXISTED_ERR',
    ACCOUNT_IS_DEACTIVE_ERR: 'ACCOUNT_IS_DEACTIVE_ERR',
    ACCOUNT_IS_BANNED_ERR: 'ACCOUNT_IS_BANNED_ERR',
    ACCOUNT_IS_PENDING_ERR: 'ACCOUNT_IS_PENDING_ERR',
    FORBIDDEN_ERR: 'FORBIDDEN_ERR',
    INVALID_PASSWORD_ERR: 'INVALID_PASSWORD_ERR',
    INVALID_CURRENT_PASSWORD_ERR: 'INVALID_CURRENT_PASSWORD_ERR',
    PARTNER_NOT_EXISTED_ERR: 'PARTNER_NOT_EXISTED_ERR',
    PARTNER_NOT_SUPPORT_ERR: 'PARTNER_NOT_SUPPORT_ERR',
    CODE_USED_ERR: 'CODE_USED_ERR',
    USER_EXIST_RESGISTER_PARTNNER_ERR: 'USER_EXIST_RESGISTER_PARTNNER_ERR',
    CODE_NOT_EXIST_ERR: 'CODE_NOT_EXIST_ERR',
    INVALID_API_PARTNER_ERR: 'INVALID_API_PARTNER_ERR',
    USER_NOT_REF_PG_ERR: 'USER_NOT_REF_PG_ERR', //user ko được giới thiệu
    USER_UNREGISTERED_MENBER_PARTNER_ERR:
      'USER_UNREGISTERED_MENBER_PARTNER_ERR',
    SECURE_HASH_INVALID_ERR: 'SECURE_HASH_INVALID_ERR',
    SECURE_HASH_TIME_OUT_MSG: 'SECURE_HASH_TIME_OUT_MSG',
    CONFIG_IS_NOT_EXISTED_ERR: 'CONFIG_IS_NOT_EXISTED_ERR',
    EVENT_IS_NOT_EXISTED_ERR: 'EVENT_IS_NOT_EXISTED_ERR',
    EVENT_ZONE_DETAIL_IS_NOT_EXISTED_ERR:
      'EVENT_ZONE_DETAIL_IS_NOT_EXISTED_ERR',
    EVENT_STAGE_IS_NOT_EXISTED_ERR: 'EVENT_STAGE_IS_NOT_EXISTED_ERR',
    EVENT_STAGE_DATE_IS_NOT_EXISTED_ERR: 'EVENT_STAGE_DATE_IS_NOT_EXISTED_ERR',
    EVENT_STAGE_DATE_TICKET_TYPE_IS_NOT_ACCEPTED_ERR:
      'EVENT_STAGE_DATE_TICKET_TYPE_IS_NOT_ACCEPTED_ERR',
    EVENT_SEAT_PRICE_IS_NOT_EXISTED_ERR: 'EVENT_SEAT_PRICE_IS_NOT_EXISTED_ERR',
    POST_IS_NOT_EXISTED_ERR: 'POST_IS_NOT_EXISTED_ERR',
    TYPE_PAYMENT_INVALID_ERR: 'TYPE_PAYMENT_INVALID_ERR',
    DEEPLINK_NOT_EXISTED_ERR: 'DEEPLINK_NOT_EXISTED_ERR',
    CART_NOT_EXISTED_ERR: 'CART_NOT_EXISTED_ERR',
    CART_DETAIL_NOT_EXISTED_ERR: 'CART_DETAIL_NOT_EXISTED_ERR',
    TRANSACTION_NOT_EXISTED_ERR: 'TRANSACTION_NOT_EXISTED_ERR',
    TEMP_SEAT_NOT_EXISTED_ERR: 'TEMP_SEAT_NOT_EXISTED_ERR',
    TEMP_QUANTITY_NOT_EXISTED_ERR: 'TEMP_QUANTITY_NOT_EXISTED_ERR',
    ERR_FROM_MOMO_ERR: 'ERR_FROM_MOMO_ERR',
    ERR_FROM_PAYOO_ERR: 'ERR_FROM_PAYOO_ERR',
    ERR_FROM_VIETQR_ERR: 'ERR_FROM_VIETQR_ERR',
    QR_CHECKED_BEFORE: 'QR_CHECKED_BEFORE',
    QR_NOT_EXISTS: 'QR_NOT_EXISTS',
    TICKET_CHECKED_BEFORE: 'TICKET_CHECKED_BEFORE',
    TICKET_NOT_EXISTS: 'TICKET_NOT_EXISTS',
    NOT_ENOUGH_TICKETS: 'NOT_ENOUGH_TICKETS',
    PAYMENT_UNSUCCESS_ERR: 'PAYMENT_UNSUCCESS_ERR',
    PAYMENT_UNSUCCESS_FROM_GATEWAY_ERR: 'PAYMENT_UNSUCCESS_FROM_GATEWAY_ERR',

    //
    PHONE_EXIST_ERR: 'PHONE_EXIST_ERR', //Số điện thoại đã tồn tại
    EMAIL_EXIST_ERR: 'EMAIL_EXIST_ERR', //Số điện thoại đã tồn tại
    VERIFY_OTP_INVALID_ERR: 'VERIFY_OTP_INVALID_ERR', //verify otp lỗi
    OTP_UNVERIFIED_ACCOUNT_ERR: 'OTP_UNVERIFIED_ACCOUNT_ERR', //Tài khoản này chưa verify
    TOKEN_REFESH_INVALID_ERR: 'TOKEN_REFESH_INVALID_ERR', //verify otp lỗi
    PASSWORD_NEW_NOT_SAME_PASSWORD_CURRENT_ERR:
      'PASSWORD_NEW_NOT_SAME_PASSWORD_CURRENT_ERR', // password mới không đucợ giống password cũ
    DUPLICATE_SEAT: 'DUPLICATE_SEAT', //trung ghe
  },
  STATUS_RES: {
    NOTFOUND: 404,
    FORBIDDEN: 403,
    UNAUTHORIZED: 401,
    BADREQUEST: 400,
    SERVER_ERROR: 500,
    SUCCESS: 200,
  },
  SESSION: {
    //keys in session
    KEY_ADMIN_USER: 'sess_admin_user',
    SET_TIME_START_CAPTCHA: 'set_time_start_captcha',
    KEY_USER_ID: 'sess_user_id',
    KEY_USER_TYPE: 'sess_user_type',
    KEY_USER_NAME: 'sess_user_name',

    KEY_ADMIN_ID: 'sess_admin_id',
    KEY_ADMIN_TYPE: 'sess_admin_type',
    KEY_ADMIN_NAME: 'sess_admin_name',

    KEY_CAPTCHA: 'sess_captcha', //save CAPTCHA number
    KEY_CAPTCHA_HOME: 'sess_captcha_home',
  },
  ROLE: {
    ADMIN: 'Admin',
    ADD: 'add',
    EDIT: 'edit',
    DELETE: 'delete',
    VIEW: 'view',
    APPROVAL: 'approval', // quyền phê duyệt chương trình
    SEND_CODE: 'send-code', // quyền send mã đến đối tác
    IMPORT_CODE: 'import-code', // quyền import mã từ đối tác
    IMPORT_STATUS: 'import-status', // quyền import trạng thái mã
    REUSE_CODE: 'reuse-code', //Quyền tái kích hoạt mã
    EXPORT_REPORT: 'export-report', //Quyền xuất báo cáo
  },
  COUNT_VALIDATE: {
    SIGNUP_USER: 1,
  },
  TYPE_ACCOUNT: {
    EMAIL: 'EMAIL',
    PHONE: 'PHONE',
    USERNAME: 'USERNAME',
  },
  ROLE_ACCOUNT: {
    USER: 'user',
    ORGANIZER: 'organizer',
    ORGANIZER_EMPLOYEE: 'organizer_employee',
    VIETQR: 'vietqr',
    INTERNAL: 'internal',
  },
  LIST_STATUS_USER: {
    PENDING: 1, //chờ duyệt
    APPROVED: 2, //Đang hoạt động
    LOCKED: 3, //Đã khóa
  },
  TYPE_PAYMNET: {
    CASH: 1, //Tiền mặt
    GATEWAYS: 2, //Cổng thanh toán
  },
  TYPE_TOKEN: {
    REFESH: 1, //type token refesh
    LOGIN: 2, //type token login
  },
  EVENT_STAGE_TYPE: {
    ZONE: 1, // ZONE MAP
    NUMBERTYPE: 2, // NUMBER TICKETS
  },
  PAYMENT_METHOD: {
    //   CASH:1,//tiền mặt
    //   VNPAY:2,//vnpay
    PAYOO_BANK_ACCOUNT: 1,
    PAYOO_CREDIT: 2,
    MOMO: 3, //momo
    VIETQR: 4, //viet_qr
    PAYOO_QR: 5, // payoo qr code
  },
  TYPE_PAYMENT_GATEWAYS: {
    ORDER: 3, //Đơn hàng
  },
  CODE_GATEWAYS: {
    //payment_gateways trong history_payment_online
    PAYOO_BANK_ACCOUNT: 1,
    PAYOO_CREDIT: 2,
    MOMO: 3,
    VIETQR: 4,
    PAYOO_QR: 5,
  },
  INFO_PAYMENT_TEXT: {
    APPOINTMENT: 'THANH TOAN',
    RECHARGE: 'THANH TOAN',
    ORDER: 'TT',
  },
  INFO_PAYMENT_STATUS_VIETQR: {
    PAYMENT_WAITING: 'PAYMENT_WAITING',
    PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
    PAYMENT_CANCEL: 'PAYMENT_CANCEL',
  },
  PASSWORD_DEFAULT:
    '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92', //123456,
  GOV: {
    UserName: 'vtix_gov',
    PassWord: 'bSkgzrW7PEYm',
  },
  EVENT_GROUP_DEFINED: {
    1: {
      slug: 'event',
      text: 'event',
    },
    2: {
      slug: 'workshop',
      text: 'workshop',
    },
    3: {
      slug: 'travel',
      text: 'travel',
    },
    4: {
      slug: 'sport',
      text: 'sport',
    },
    5: {
      slug: 'family-playground',
      text: 'family_playground',
    },
    6: {
      slug: 'travel',
      text: 'travel',
    },
  } as {
    [key: string]: any;
  },
};

export default Constants;
