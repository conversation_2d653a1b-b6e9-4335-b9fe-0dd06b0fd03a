import {
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Connection,
  PublicKey as PublicKeyWeb3,
  SendTransactionError,
} from '@solana/web3.js';
import { publicKey, PublicKey as PublicKeyUmi } from '@metaplex-foundation/umi';

import {
  CreateNftInput,
  Metaplex,
  keypairIdentity as keypairIdentityJs, // mint NFT
} from '@metaplex-foundation/js';

import { Op } from 'sequelize';
import {
  keypairIdentity, // mint cNFT
  generateSigner,
  createSignerFromKeypair,
  percentAmount,
  none,
} from '@metaplex-foundation/umi';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';

import {
  mplTokenMetadata,
  createNft,
} from '@metaplex-foundation/mpl-token-metadata';

import { Buffer } from 'buffer';

import {
  mplBubblegum,
  createTree,
  fetchMerkleTree,
  fetchTreeConfigFromSeeds,
  mintV1, // mint cNFT
  findLeafAssetIdPda,
  parseLeafFromMintV1Transaction,
  getAssetWithProof,
  burn,
  updateMetadata,
} from '@metaplex-foundation/mpl-bubblegum';
import { getConcurrentMerkleTreeAccountSize } from '@solana/spl-account-compression';

import { numberFormatter, printConsoleSeparator } from '../utils/nft/helpers';

import config from '../config/setting';
import Common from '../common/common';
import Constants from '../common/constants';
import moment from 'moment-timezone';
import fs from 'fs';
import {
  UpdateMetadataInstructionAccounts,
  UpdateMetadataInstructionArgs,
} from '@metaplex-foundation/mpl-bubblegum/dist/src/generated/instructions/updateMetadata';
import {
  BurnInstructionAccounts,
  BurnInstructionArgs,
} from '@metaplex-foundation/mpl-bubblegum/dist/src/generated/instructions/burn';
import { CNftTemp } from '@cnft_temps/models/cnft_temp.model';
import { CNftTempsService } from '@cnft_temps/cnft_temps.services';
import { dasApi } from '@metaplex-foundation/digital-asset-standard-api';
import { base58 } from '@metaplex-foundation/umi/serializers';

const fileNameLog = 'activity_nft';
const HELIUS_RPC_URL =
  'https://mainnet.helius-rpc.com/?api-key=76fc60a0-dd79-44a8-a3ec-ac242a4ed194';
const PRIORITY_FEE_MICROLAMPORTS = 5000; // 0.000005 SOL = 5,000 µLamports
const MAX_RETRIES = 5;
const RETRY_DELAYS = [300, 800, 1500, 3000, 5000]; // Exponential backoff
const VALID_DEPTH_SIZE_PAIRS = [
  { maxDepth: 14, maxBufferSize: 64 }, // Your current config
  { maxDepth: 15, maxBufferSize: 64 },
  { maxDepth: 20, maxBufferSize: 256 },
  { maxDepth: 24, maxBufferSize: 512 },
  { maxDepth: 26, maxBufferSize: 1024 },
  { maxDepth: 30, maxBufferSize: 2048 },
];

class ActivityNft {
  static async buildNFT(
    order_id,
    event_zone_detail_info,
    event,
    element,
    userinfo,
    event_date,
    event_stage,
  ) {
    // get cnft_temp
    const getCnftTempDetail = async (where_array) => {
      return await CNftTemp.findOne({
        where: where_array,
        raw: true, // <----------- Magic is here
        nest: true, // <----------- Magic is here
      });
    };
    const cnft_detail = await getCnftTempDetail({
      deleted: false,
      event_id: event.id,
      event_stage_id: event_stage.id,
      event_date_id: event_date.id,
      event_zone_detail_id: event_zone_detail_info
        ? event_zone_detail_info.id
        : 0,
      event_seat_id: element.event_seat_id,
      pos: element.pos,
      mint_address: {
        [Op.not]: null,
      },
      cart_detail_temp_id: '0',
    });
    console.log('cnft_detail: ', cnft_detail);
    if (!Common.isEmpty(cnft_detail) && cnft_detail) {
      await CNftTemp.update(
        {
          cart_detail_temp_id: element.id,
        },
        {
          where: { id: cnft_detail.id },
        },
      );

      return cnft_detail.mint_address;
    }
    return '';
  }
  static async buildNFT_BK(
    req,
    order_id,
    event_zone_detail_info,
    event,
    element,
    userinfo,
    event_date,
    event_stage,
  ) {
    //tạo log header
    Common.logToFile('HEADER-build-nft', req.headers, fileNameLog);
    // await generateWallet();
    // return true;

    // dung chung cho ca hai loai ve
    const nft_name = event_zone_detail_info.nft_name
      ? event_zone_detail_info.nft_name + ' #' + element.seat_name
      : config.config_nft.nft_name_default + ' #' + element.seat_name;
    const nft_symbol = event_zone_detail_info.nft_symbol
      ? event_zone_detail_info.nft_symbol
      : config.config_nft.nft_symbol_default;
    const nft_image = event_zone_detail_info.nft_image
      ? event_zone_detail_info.nft_image
      : config.config_nft.nft_image_Default;

    if (event_stage.stage_type == Constants.EVENT_STAGE_TYPE.ZONE) {
      // create json file, save to folder upload

      const attribute_nft =
        '{"name": "' +
        nft_name +
        '","symbol": "' +
        event_zone_detail_info.nft_symbol +
        '","description": "' +
        event.name_vi +
        '","background_color": "000000","external_url": "' +
        config.protocol +
        '://vtix.vn/en/event/' +
        event.slug_en +
        '","image": "' +
        nft_image +
        '","attributes":[{"trait_type": "EVENT","value": "' +
        event.name_vi +
        '"},{"trait_type": "Zone","value": "' +
        event_zone_detail_info.zone_name +
        '"},{"trait_type": "Seat","value": "' +
        element.seat_name +
        '"},{"trait_type": "Email","value": "' +
        userinfo.email +
        '"},{"trait_type": "Event Time","value": "' +
        moment(event_date.show_date * 1000)
          .tz('Asia/Ho_Chi_Minh')
          .format() +
        '"}]}';
      const linkMetadata = Common.createJsonFile(
        req,
        'tickets/' + event.id,
        attribute_nft,
        userinfo.id + '_' + order_id + '.json',
      );
      console.log('LINK METADATA: ' + linkMetadata);
      // UPDATE: mint cnft
      // linkMetadata = 'https://raw.githubusercontent.com/iioollun/demo_cnft/main/ca_cnft.json';
      // console.log('LINK METADATA: ' + linkMetadata);
      // var collection_detail = await buildTreeNftColections(req, linkMetadata);
      // return true;

      // var mintNFTRes = await mintCNFT(linkMetadata, nft_name, nft_symbol, collection_detail);
      // if(mintNFTRes){
      //   return mintNFTRes;
      // }

      // mint nft
      const mintNFTResponse = await mintNFT(linkMetadata, nft_name, nft_symbol);
      if (mintNFTResponse) {
        // update mintAddress
        const mint_address = mintNFTResponse.mintAddress.toString();
        // console.log(mintNFTResponse.mintAddress.toString());
        // console.log(element.id);
        return mint_address;
      }
    }
    if (event_stage.stage_type == Constants.EVENT_STAGE_TYPE.NUMBERTYPE) {
      // create json file, save to folder upload
      // let attribute_nft = '{"name": "'+nft_name+'","symbol": "'+event_zone_detail_info.nft_symbol+'","description": "'+event.name_vi+'","background_color": "000000","external_url": "'+config.protocol+'://vtix.vn/en/event/'+event.slug_en+'","image": "'+nft_image+'","attributes":[{"trait_type": "EVENT","value": "'+event.name_vi+'"},{"trait_type": "Ticket Type","value": "'+event_zone_detail_info.name+'"},{"trait_type": "Ticket Num.","value": "'+element.seat_name+'"},{"trait_type": "Email","value": "'+userinfo.email+'"},{"trait_type": "Event Time","value": "'+moment(event_date.show_date * 1000).tz('Asia/Ho_Chi_Minh').format()+'"}]}';
      // let linkMetadata = Common.createJsonFile( req, 'tickets/' + event.id, attribute_nft, userinfo.id + '_' + order_id + '.json');
      // console.log('LINK METADATA: ' + linkMetadata);
      // var mintNFTRes = await mintCNFT(linkMetadata, nft_name, nft_symbol);
      // if(mintNFTRes){
      //   return mintNFTRes;
      // }
      // mint nft
      // const mintNFTResponse = await mintNFT(linkMetadata, nft_name, nft_symbol);
      // if (mintNFTResponse) {
      //   // update mintAddress
      //   const mint_address = mintNFTResponse.mintAddress.toString();
      //   console.log(mintNFTResponse.mintAddress.toString());
      //   console.log(element.id);
      //   return mint_address;
      // }
    }
    return false;
  }

  static async buildNFTAttendees(
    req,
    order_id,
    ticket_info,
    event,
    element,
    userinfo,
    event_date,
  ) {
    //tạo log header
    Common.logToFile('HEADER-build-nft', req.headers, fileNameLog);
    // await generateWallet();
    // return true;

    // dung chung cho ca hai loai ve
    const nft_name = ticket_info.nft_name
      ? ticket_info.nft_name + ' #' + element.seat_name
      : config.config_nft.nft_name_default + ' #' + element.seat_name;
    const nft_symbol = ticket_info.nft_symbol
      ? ticket_info.nft_symbol
      : config.config_nft.nft_symbol_default;
    const nft_image = ticket_info.nft_image
      ? ticket_info.nft_image
      : config.config_nft.nft_image_Default;

    // create json file, save to folder upload

    const attribute_nft =
      '{"name": "' +
      nft_name +
      '","symbol": "' +
      element.seat_name +
      '","description": "' +
      event.name_vi +
      '","background_color": "000000","external_url": "https://vtix.vn/","image": "' +
      nft_image +
      '","attributes":[{"trait_type": "EVENT","value": "' +
      event.name_vi +
      '"},{"trait_type": "Ticket Type","value": "' +
      ticket_info.name +
      '"},{"trait_type": "Ticket Num.","value": "' +
      element.seat_name +
      '"},{"trait_type": "Email","value": "' +
      userinfo.email +
      '"},{"trait_type": "Event Time","value": "' +
      moment(event_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format() +
      '"}]}';
    const linkMetadata = Common.createJsonFile(
      req,
      'tickets/' + event.id,
      attribute_nft,
      userinfo.id + '_' + order_id + '.json',
    );
    console.log('LINK METADATA: ' + linkMetadata);
    const mintNFTRes = await mintCNFT(
      linkMetadata,
      nft_name,
      element.seat_name,
    );
    if (mintNFTRes) {
      return mintNFTRes;
    }

    return false;
  }

  static async buildCNFTTemp(
    req,
    element,
    order_id,
    ticket_info,
    event_detail,
    event_date,
  ) {
    //tạo log header
    Common.logToFile('HEADER-build-cnft-temp', req.headers, fileNameLog);
    // await generateWallet();
    // return true;

    // dung chung cho ca hai loai ve
    const nft_name = ticket_info.nft_name
      ? ticket_info.nft_name + ' #' + element.seat_name
      : config.config_nft.nft_name_default + ' #' + element.seat_name;
    const nft_symbol = ticket_info.nft_symbol
      ? ticket_info.nft_symbol
      : config.config_nft.nft_symbol_default;
    const nft_image = ticket_info.nft_image
      ? ticket_info.nft_image
      : config.config_nft.nft_image_Default;

    // create json file, save to folder upload

    const attribute_nft =
      '{"name": "' +
      nft_name +
      '","symbol": "' +
      element.seat_name +
      '","description": "' +
      event_detail.name_vi +
      '","background_color": "000000","external_url": "https://vtix.vn/","image": "' +
      nft_image +
      '","attributes":[{"trait_type": "EVENT","value": "' +
      event_detail.name_vi +
      '"},{"trait_type": "Zone","value": "' +
      ticket_info.zone_name +
      '"},{"trait_type": "Seat","value": "' +
      element.seat_name +
      '"},{"trait_type": "Event Time","value": "' +
      moment(event_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format() +
      '"}]}';
    const linkMetadata = await Common.createJsonFile(
      req,
      'tickets/' + event_detail.id,
      attribute_nft,
      order_id + '.json',
    );
    console.log('LINK METADATA: ' + linkMetadata);
    const mintNFTRes = await mintCNFT(
      linkMetadata,
      nft_name,
      element.seat_name,
    );
    if (mintNFTRes) {
      return mintNFTRes;
    }

    return false;
  }

  static async convertCNFTTempData(
    req,
    element,
    order_id,
    ticket_info,
    event_detail,
    event_date,
  ) {
    // await generateWallet();
    // return true;

    // dung chung cho ca hai loai ve
    const nft_name = ticket_info.nft_name
      ? ticket_info.nft_name + ' #' + element.seat_name
      : config.config_nft.nft_name_default + ' #' + element.seat_name;
    const nft_symbol = ticket_info.nft_symbol
      ? ticket_info.nft_symbol
      : config.config_nft.nft_symbol_default;
    const nft_image = ticket_info.nft_image
      ? ticket_info.nft_image
      : config.config_nft.nft_image_Default;

    // create json file, save to folder upload

    const attribute_nft =
      '{"name": "' +
      nft_name +
      '","symbol": "' +
      element.seat_name +
      '","description": "' +
      event_detail.name_vi +
      '","background_color": "000000","external_url": "https://vtix.vn/","image": "' +
      nft_image +
      '","attributes":[{"trait_type": "EVENT","value": "' +
      event_detail.name_vi +
      '"},{"trait_type": "Zone","value": "' +
      ticket_info.zone_name +
      '"},{"trait_type": "Seat","value": "' +
      element.seat_name +
      // '"},{"trait_type": "Order ID","value": "' +
      // order_id +
      '"},{"trait_type": "Event Time","value": "' +
      moment(event_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format() +
      '"}]}';

    return {
      path: 'tickets/' + event_detail.id,
      attribute_nft,
      nft_name,
      file_name: order_id + '.json',
    };
  }

  static async buildCNFTTempv2(linkMetadata, nft_name, element) {
    const mintNFTRes = await mintCNFT(
      linkMetadata,
      nft_name,
      element.seat_name,
    );
    if (mintNFTRes) {
      return mintNFTRes;
    }

    return '';
  }

  static async buildMerkleTree(req, linkMetadata) {
    return await buildMerkleTree(req, linkMetadata);
  }

  static async getLeafID(transaction) {
    return await getLeafID(transaction);
  }

  static async burnCNFT(assetId) {
    return await burnCnftDirect(assetId);
  }

  static async getAllCnfts() {
    return await getAllCnftsWithPaging();
  }

  static async getCNFTInfoByAssetId(assetId) {
    return await getCNFTInfoByAssetId(assetId);
  }

  static async getCNFTTransactionByAssetId(assetId) {
    return await getCNFTTransactionByAssetId(assetId);
  }

  static async createNFT(
    req,
    element,
    order_id,
    ticket_info,
    event_detail,
    event_date,
  ) {
    //tạo log header
    Common.logToFile('HEADER-build-cnft-temp', req.headers, fileNameLog);
    // await generateWallet();
    // return true;

    // dung chung cho ca hai loai ve
    const nft_name = ticket_info.nft_name
      ? ticket_info.nft_name + ' #' + element.seat_code
      : config.config_nft.nft_name_default + ' #' + element.seat_code;
    const nft_symbol = ticket_info.nft_symbol
      ? ticket_info.nft_symbol
      : config.config_nft.nft_symbol_default;
    const nft_image = ticket_info.nft_image
      ? ticket_info.nft_image
      : config.config_nft.nft_image_Default;

    // create json file, save to folder upload

    const attribute_nft =
      '{"name": "' +
      nft_name +
      '","symbol": "' +
      element.seat_code +
      '","description": "' +
      event_detail.name_vi +
      '","background_color": "000000","external_url": "https://vtix.vn/","image": "' +
      nft_image +
      '","attributes":[{"trait_type": "EVENT","value": "' +
      event_detail.name_vi +
      '"},{"trait_type": "Zone","value": "' +
      ticket_info.zone_name +
      '"},{"trait_type": "Seat","value": "' +
      element.seat_code +
      '"},{"trait_type": "Event Time","value": "' +
      moment(event_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format() +
      '"}]}';
    const linkMetadata = Common.createJsonFile(
      req,
      'tickets/' + event_detail.id,
      attribute_nft,
      order_id + '.json',
    );
    const mintNFTRes = await mintCNFT(
      linkMetadata,
      nft_name,
      element.seat_code,
    );
    console.log('---------------------------mintNFTRes :>> ', mintNFTRes);
    if (mintNFTRes) {
      return mintNFTRes;
    }

    return null;
  }
}

async function buildMerkleTree(req, linkMetadata) {
  console.log('Merkle Tree');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // load the env variables and store the cluster RPC url
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  console.log('CLUSTER_URL');
  console.log(CLUSTER_URL);

  // define the address the tree will live at
  const umi = createUmi(CLUSTER_URL)
    .use(mplTokenMetadata())
    .use(mplBubblegum());

  // Create a keypair from your private key
  const payer = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  const wallet = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  console.log('Payer address:', payer.publicKey);
  console.log('Wallet address:', wallet.publicKey);

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // create a new rpc connection, using the ReadApi wrapper

  await getSolBalance();

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////

  /*
    Define our tree size parameters
  */
  const maxDepthSizePair = {
    maxDepth: 14, // cong thuc: tong so nodes (nfts) = 2 ^ maxDepth ~ 45 nodes(nfts) = 2^6 = 64 => ok
    maxBufferSize: 64, // theo cap so trong link https://github.com/solana-labs/solana-program-library/blob/master/account-compression/sdk/src/constants/index.ts
  };
  const canopyDepth = 0; // ~ lay theo link nay cho nhanh https://compressed.app/

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // calculate the space available in the tree
  await calcSolBuiltMerkleTree(
    maxDepthSizePair.maxDepth,
    maxDepthSizePair.maxBufferSize,
    canopyDepth,
  );

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // Actually allocate the tree on chain
  const myKeypairUmi = createSignerFromKeypair(umi, payer);
  // ADD PAYER VÀO UMI
  umi.use(keypairIdentity(myKeypairUmi));
  // umi.use(signerIdentity(myKeypairUmi));

  // MOI CAY TUONG DUONG VOI MOT BO SUU TAP
  // MOI KHI TAO EVENT MOI THI PHAI TAO 1 CAY MOI

  const merkleTree = await generateSigner(umi);

  console.log('merkleTree - NEW');
  console.log(merkleTree);
  const builder = await createTree(umi, {
    merkleTree,
    maxDepth: maxDepthSizePair.maxDepth, // cong thuc: tong so nodes (nfts) = 2 ^ maxDepth ~ 45 nodes(nfts) = 2^6 = 64 => ok
    maxBufferSize: maxDepthSizePair.maxBufferSize, // theo cap so trong link https://github.com/solana-labs/solana-program-library/blob/master/account-compression/sdk/src/constants/index.ts
    canopyDepth: canopyDepth, // maxDepth - canopyDepth <= 10 ~ lay theo link nay cho nhanh https://compressed.app/
  });
  const result = await builder.sendAndConfirm(umi);
  console.log('RESULT MERKLE TREE: ', result);

  // // FETCHING TREE ACCOUNT & TREE CONFIG
  const merkleTreeAccount = await fetchMerkleTree(umi, merkleTree as any);
  const treeConfig = await fetchTreeConfigFromSeeds(umi, {
    merkleTree: merkleTree as any,
  });

  console.log('TREE ACCOUNT');
  console.log(merkleTreeAccount);
  console.log('TREE CONFIG');
  console.log(treeConfig);
  return merkleTree;
}

async function buildCollection(req, linkMetadata) {
  console.log('Collection Build');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // load the env variables and store the cluster RPC url
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  console.log('CLUSTER_URL');
  console.log(CLUSTER_URL);

  // define the address the tree will live at
  const umi = createUmi(CLUSTER_URL)
    .use(mplTokenMetadata())
    .use(mplBubblegum());

  // Create a keypair from your private key
  const payer = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  const wallet = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  // const wallet = await loadKeypair(config.config_nft.wallet_keypair);
  // const payer = await loadKeypair(config.config_nft.nft_keypair);

  console.log('Payer address:', payer.publicKey);
  console.log('Wallet address:', wallet.publicKey);

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  /*
    Create the actual NFT collection (using the normal Metaplex method)
    (nothing special about compression here)
  */

  // // define the metadata to be used for creating the NFT collection
  const attribute_nft =
    '{"name": "VTIX Collection Demo","symbol": "VTCD","external_url": "' +
    config.config_nft.collection_url +
    '","image": "https://staging.vtix.vn/event/brand-review-tour/brand-review-tour.jpg","attributes":[{"trait_type": "EVENT","value": "VTIX Collection Demo"},{"trait_type": "Show Round","value": "Sa, 11 May 2024, 07:30"},{"trait_type": "Venue","value": "Vinacacao & HUM restaurant"},{"trait_type": "Public Sale","value": "Fr, 03 May 2024, 11:00"}]}';
  // let linkMetadataCollection = Common.createJsonFile( req, 'collections/test', attribute_nft, 'test.json');

  // const myProgram = umi.programs.get('Associated Token Account Program');
  // // console.log(myProgram);

  const linkMetadataCollection =
    'https://raw.githubusercontent.com/iioollun/demo_cnft/main/collection.json';
  console.log('LINK METADATA - COLLECTION: ' + linkMetadataCollection);
  const collectionMint = await generateSigner(umi);
  console.log('collectionMint - NEW');
  console.log(collectionMint);
  const collection = await createNft(umi, {
    mint: collectionMint,
    name: 'VTIX Collection Demo',
    symbol: 'VTX 789DA',
    uri: linkMetadataCollection,
    sellerFeeBasisPoints: percentAmount(1), // 1%
    //    isMutable: false,
    isCollection: true,
  }).sendAndConfirm(umi);
  console.log('COLLECTION');
  console.log(collection);
}

// async function verifyCollection(req, linkMetadata) {
//   console.log('Verify Collection');
//   //////////////////////////////////////////////////////////////////////////////
//   //////////////////////////////////////////////////////////////////////////////
//   // load the env variables and store the cluster RPC url
//   const CLUSTER_URL = config.config_nft.RPC_URL
//     ? config.config_nft.RPC_URL
//     : clusterApiUrl('devnet');
//   console.log('CLUSTER_URL');
//   console.log(CLUSTER_URL);
//
//   // define the address the tree will live at
//   const umi = createUmi(CLUSTER_URL)
//     .use(mplTokenMetadata())
//     .use(mplBubblegum());
//
//   // Create a keypair from your private key
//   const payer = await umi.eddsa.createKeypairFromSecretKey(
//     new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
//   );
//
//   const wallet = await umi.eddsa.createKeypairFromSecretKey(
//     new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
//   );
//
//   // const wallet = await loadKeypair(config.config_nft.wallet_keypair);
//   // const payer = await loadKeypair(config.config_nft.nft_keypair);
//
//   console.log('Payer address:', payer.publicKey);
//   console.log('Wallet address:', wallet.publicKey);
//
//   //////////////////////////////////////////////////////////////////////////////
//   //////////////////////////////////////////////////////////////////////////////
//   // VERIFY COLLECTIONS
//
//   // Create a keypair from your private key
//
//   const verify_collection = await verifyCollectionV1(umi, {
//     metadata,
//     collectionMint,
//     authority: collectionAuthority,
//   }).sendAndConfirm(umi);
//   console.log('VERIFY COLLECTION');
//   console.log(verify_collection);
// }

async function mintCNFT(linkMetadata, nft_name, nft_symbol) {
  console.log('CNFT Build');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // load the env variables and store the cluster RPC url
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  console.log('CLUSTER_URL');
  console.log(CLUSTER_URL);

  // define the address the tree will live at
  const umi = createUmi(CLUSTER_URL)
    .use(mplTokenMetadata())
    .use(mplBubblegum());

  // Create a keypair from your private key
  const payer = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  const wallet = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  // const wallet = await loadKeypair(config.config_nft.wallet_keypair);
  // const payer = await loadKeypair(config.config_nft.nft_keypair);

  umi.use(keypairIdentity(payer));
  console.log('Payer address:', payer.publicKey);
  console.log('Wallet address:', wallet.publicKey);

  // await getSolBalance();

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////

  const merkleTreeKeypair = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(
      // Buffer.from(JSON.parse("[200,80,50,63,57,165,94,126,254,146,140,66,142,82,245,207,8,244,69,59,61,186,206,21,155,46,42,219,76,14,217,25,213,192,182,103,177,179,80,99,69,130,210,11,15,150,146,79,245,67,67,96,176,196,117,107,116,166,72,8,43,219,110,242]"))
      Buffer.from(JSON.parse(config.config_nft.merkle_tree_keypair)),
    ),
  );

  // const collectionMintKeypair = await umi.eddsa.createKeypairFromSecretKey(new Uint8Array(
  //   Buffer.from(JSON.parse(config.config_nft.collection_mint_keypair))
  // ))
  /*
    Mint a compressed NFT without collection
  */
  try {
    const dataCNFT = await mintV1(umi, {
      leafOwner: payer.publicKey,
      merkleTree: merkleTreeKeypair.publicKey,
      metadata: {
        name: nft_name,
        uri: linkMetadata,
        sellerFeeBasisPoints: 100, // 1%
        collection: none(),
        creators: [
          { address: umi.identity.publicKey, verified: true, share: 100 },
        ],
      },
    }).sendAndConfirm(umi, {
      confirm: { commitment: 'confirmed' },
      send: {
        skipPreflight: true,
        maxRetries: 3,
      },
    });

    console.log('dataCNFT: ', dataCNFT);
    console.log('signature: ', dataCNFT.signature);

    // var leaf = await parseLeafFromMintV1Transaction(umi, signature);
    // var assetId = findLeafAssetIdPda(umi, {
    //   merkleTree: merkleTreeKeypair.publicKey,
    //   leafIndex: leaf.nonce
    // });
    // // console.log("LEAF:")
    // // console.log(leaf);
    // console.log("assetId:")
    // console.log(assetId[0].toString());

    return dataCNFT.signature;
  } catch (error) {
    console.error('Error minting CNFT:', error);
    return '';
  }

  /*
    Mint a compressed NFT to collection
  */
  // const cnft = await mintToCollectionV1(umi, {
  //   leafOwner: payer.publicKey,
  //   merkleTree: merkleTreeKeypair.publicKey,
  //   collectionMint: collectionMintKeypair.publicKey,
  //   metadata: {
  //     name: 'VTX 789DA #A12',
  //     symbol: '789DA',
  //     uri: 'https://raw.githubusercontent.com/iioollun/demo_cnft/main/cnft.json',
  //     sellerFeeBasisPoints: 100, // 1%
  //     // isMutable: false,
  //     collection: { key: collectionMintKeypair.publicKey, verified: true },
  //     creators: [
  //       { address: umi.identity.publicKey, verified: true, share: 100 },
  //     ],
  //   },
  //   collectionAuthority: myKeypairUmi,
  // }).sendAndConfirm(umi)
  // console.log("CNFT")
  // console.log(cnft);
}

async function getLeafID(transaction) {
  try {
    console.log('CNFT get LEAF');
    //////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////
    // load the env variables and store the cluster RPC url
    const CLUSTER_URL = config.config_nft.RPC_URL
      ? config.config_nft.RPC_URL
      : clusterApiUrl('devnet');
    console.log('CLUSTER_URL');
    console.log(CLUSTER_URL);

    // define the address the tree will live at
    const umi = createUmi(CLUSTER_URL)
      .use(mplTokenMetadata())
      .use(mplBubblegum());

    // Create a keypair from your private key
    const payer = await umi.eddsa.createKeypairFromSecretKey(
      new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
    );

    const wallet = await umi.eddsa.createKeypairFromSecretKey(
      new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
    );

    // const wallet = await loadKeypair(config.config_nft.wallet_keypair);
    // const payer = await loadKeypair(config.config_nft.nft_keypair);

    umi.use(keypairIdentity(payer));
    console.log('Payer address:', payer.publicKey);
    console.log('Wallet address:', wallet.publicKey);

    await getSolBalance();

    const signature = new Uint8Array(
      Buffer.from(JSON.parse('[' + transaction + ']')),
    );

    console.log('signature: ');
    console.log(signature);

    const merkleTreeKeypair = await umi.eddsa.createKeypairFromSecretKey(
      new Uint8Array(
        Buffer.from(JSON.parse(config.config_nft.merkle_tree_keypair)),
      ),
    );

    const leaf = await parseLeafFromMintV1Transaction(umi, signature);
    console.log('LEAF:');
    console.log(leaf);

    const assetId = findLeafAssetIdPda(umi, {
      merkleTree: merkleTreeKeypair.publicKey,
      leafIndex: leaf.nonce,
    });
    console.log('assetId: ', assetId);
    return assetId[0];
  } catch (error) {
    console.error('Error getLeafID:', error);
    return null;
  }
}

async function burnCNFT(assetId) {
  console.log('CNFT get LEAF');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // load the env variables and store the cluster RPC url
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  console.log('CLUSTER_URL');
  console.log(CLUSTER_URL);

  const connection = new Connection(CLUSTER_URL, 'confirmed');

  // define the address the tree will live at
  const umi = createUmi(CLUSTER_URL)
    .use(mplTokenMetadata())
    .use(mplBubblegum());

  // Create a keypair from your private key
  const payer = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  const wallet = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  // const wallet = await loadKeypair(config.config_nft.wallet_keypair);
  // const payer = await loadKeypair(config.config_nft.nft_keypair);

  umi.use(keypairIdentity(payer));
  console.log('Payer address:', payer.publicKey);
  console.log('Wallet address:', wallet.publicKey);

  await getSolBalance();

  // let assetid = '5jAQvfnwHFq6GZqWdtySBEk7hS2PpWXW8demCvRh7pTg'
  console.log('assetId: ', assetId);
  try {
    const assetWithProof = await getAssetWithProof(umi, assetId);
    const burnDetail = await burn(umi, <
      BurnInstructionAccounts & BurnInstructionArgs
    >{
      ...assetWithProof,
      leafOwner: payer.publicKey,
    }).sendAndConfirm(umi);
    console.log('BURN DETAIL: ', burnDetail);
  } catch (error) {
    if (error instanceof SendTransactionError) {
      const logs = await error.getLogs(connection);
      console.error('Transaction logs:', logs);
    } else {
      console.error('An unexpected error occurred:', error);
    }
  }

  return true;
}

async function burnCnftDirect(assetId) {
  // 1. Initialize Umi with compression support
  const umi = createUmi(HELIUS_RPC_URL) // Must use Helius
    .use(mplBubblegum())
    .use(dasApi())
    .use(mplTokenMetadata());
  // 2. Load owner keypair (must be current NFT owner)
  let ownerSecretKey;
  try {
    ownerSecretKey = JSON.parse(config.config_nft.nft_keypair);
  } catch (error) {
    console.error('Failed to parse owner secret key:', error);
    return false;
  }
  const ownerKeypair = umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(ownerSecretKey)),
  );
  umi.use(keypairIdentity(ownerKeypair));

  try {
    // Verify asset existence first
    const asset = await umi.rpc.getAsset(assetId).catch(() => null);
    console.info(`>>>>>>>>> Asset ${assetId} found: ${JSON.stringify(asset)}`);
    if (!asset) {
      console.error(`Asset ${assetId} not found`);
      throw new Error(`Asset ${assetId} not found`);
    }

    // Validate Merkle tree account
    const treeAccount = await umi.rpc.getAccount(asset.compression.tree);
    console.info(`>>>>>>>>>>> TreeAccount exist`);
    if (!treeAccount.exists) {
      console.error(`Merkle tree ${asset.compression.tree} does not exist`);
      throw new Error(`Merkle tree ${asset.compression.tree} does not exist`);
    }
    // Get asset proof with retries
    const assetWithProof = await getAssetWithProof(umi, assetId, {
      truncateCanopy: true,
    });

    // Execute burn with proper fee payer
    await burn(umi, <BurnInstructionAccounts & BurnInstructionArgs>{
      ...assetWithProof,
      leafOwner: ownerKeypair.publicKey,
      authority: ownerKeypair, // Sign with owner
    }).sendAndConfirm(umi);

    console.info(`-------------- Burned cNFT: ${assetId}`);

    return true;
  } catch (error) {
    console.error(`Failed to burn ${assetId}:`, error);
    return false;
  }
}

// Delete all cnft garbage
async function burnAllCnftLeft() {
  // 1. Initialize Umi with compression support
  const umi = createUmi(HELIUS_RPC_URL)
    .use(mplBubblegum())
    .use(dasApi())
    .use(mplTokenMetadata());
  // 2. Load owner keypair (must be current NFT owner)
  let ownerSecretKey;
  try {
    ownerSecretKey = JSON.parse(config.config_nft.nft_keypair);
  } catch (error) {
    console.error('Failed to parse owner secret key:', error);
    return false;
  }
  const ownerKeypair = umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(ownerSecretKey)),
  );
  umi.use(keypairIdentity(ownerKeypair));

  try {
    // 3. Get all assets in wallet
    const allAssets = await umi.rpc.getAssetsByOwner({
      owner: ownerKeypair.publicKey,
    });

    console.log(`Found ${allAssets.items.length} assets in wallet`);

    // 4. Filter compressed NFTs
    const cNfts = allAssets.items.filter(
      (asset) =>
        asset.compression?.compressed === true &&
        asset.burnt === false &&
        asset.creators[0].address !== ownerKeypair.publicKey.toString(),
    );

    console.log(
      `Found ${cNfts.length} compressed NFTs to burn`,
      cNfts.map((cnft) => cnft.id),
    );

    // 5. Burn each cNFT
    for (const cnft of cNfts) {
      try {
        // Verify asset existence first
        const asset = await umi.rpc.getAsset(cnft.id).catch(() => null);
        console.info(
          `>>>>>>>>> Asset ${cnft.id} found: ${JSON.stringify(asset)}`,
        );
        if (!asset) {
          console.error(`Asset ${cnft.id} not found`);
          throw new Error(`Asset ${cnft.id} not found`);
        }

        // Validate Merkle tree account
        const treeAccount = await umi.rpc.getAccount(asset.compression.tree);
        console.info(`>>>>>>>>>>> TreeAccount: ${treeAccount}`);
        if (!treeAccount.exists) {
          console.error(`Merkle tree ${asset.compression.tree} does not exist`);
          throw new Error(
            `Merkle tree ${asset.compression.tree} does not exist`,
          );
        }
        // Get asset proof with retries
        const assetWithProof = await getAssetWithProof(umi, cnft.id, {
          truncateCanopy: true,
        });

        // Execute burn with proper fee payer
        await burn(umi, <BurnInstructionAccounts & BurnInstructionArgs>{
          ...assetWithProof,
          leafOwner: ownerKeypair.publicKey,
          authority: ownerKeypair, // Sign with owner
        }).sendAndConfirm(umi);

        console.info(`-------------- Burned cNFT: ${cnft.id}`);
      } catch (burnError) {
        console.error(`Failed to burn ${cnft.id}:`, burnError);
      }
    }

    return true;
  } catch (error) {
    console.error('Critical error:', error);
    return false;
  }
}

async function getAllCnfts(limit = 1000): Promise<any> {
  const umi = createUmi(HELIUS_RPC_URL)
    .use(mplBubblegum())
    .use(dasApi())
    .use(mplTokenMetadata());
  let ownerSecretKey;
  try {
    ownerSecretKey = JSON.parse(config.config_nft.nft_keypair);
  } catch (error) {
    console.error('Failed to parse owner secret key:', error);
    return false;
  }
  const ownerKeypair = umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(ownerSecretKey)),
  );
  umi.use(keypairIdentity(ownerKeypair));

  const allAssets = await umi.rpc.getAssetsByOwner({
    owner: ownerKeypair.publicKey,
    limit,
    sortBy: {
      sortBy: 'created',
      sortDirection: 'desc',
    },
  });
  console.log(`Owner address: ${ownerKeypair.publicKey.toString()}`);
  console.log(`Found ${allAssets.items.length} assets in wallet`);

  const cNfts = allAssets.items.filter(
    (asset) =>
      asset.compression?.compressed === true &&
      asset.interface === 'V1_NFT' &&
      asset.creators[0].address === ownerKeypair.publicKey.toString() &&
      asset.burnt === false,
  );
  return cNfts;
}

async function getAllCnftsWithPaging(): Promise<any> {
  const umi = createUmi(HELIUS_RPC_URL)
    .use(mplBubblegum())
    .use(dasApi())
    .use(mplTokenMetadata());

  // 1. Verify keypair parsing
  let ownerKeypair;
  try {
    const ownerSecretKey = JSON.parse(config.config_nft.nft_keypair);
    ownerKeypair = umi.eddsa.createKeypairFromSecretKey(
      new Uint8Array(Buffer.from(ownerSecretKey)),
    );
  } catch (error) {
    console.error('Keypair error:', error);
    throw new Error('Invalid keypair configuration');
  }
  umi.use(keypairIdentity(ownerKeypair));

  let allAssets: any[] = [];
  let cursor: any;
  let hasMore = true;
  const limit = 1000; // Max per page
  let page = 1;
  let retries = 3;

  while (hasMore && retries > 0) {
    try {
      const response = await umi.rpc.getAssetsByOwner({
        owner: ownerKeypair.publicKey,
        page: page++,
        limit,
        after: cursor,
        sortBy: {
          sortBy: 'created',
          sortDirection: 'desc',
        },
      });

      // Filter for compressed NFTs
      const compressedAssets = response.items.filter(
        (asset) =>
          asset.compression?.compressed === true &&
          asset.interface === 'V1_NFT' &&
          asset.creators[0]?.address === ownerKeypair.publicKey.toString() &&
          asset.burnt === false,
      );

      allAssets = [...allAssets, ...compressedAssets];
      
      // Check if we've reached the end of the results
      hasMore = response.items.length === limit;
      cursor = response.cursor;
      
      // Reset retry counter on successful request
      retries = 3;
    } catch (error) {
      console.error('RPC Error:', error);
      retries--;
      if (retries === 0) {
        throw new Error(`Failed to fetch assets after multiple retries: ${error.message}`);
      }
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  if (allAssets.length === 0) {
    throw new Error('No compressed NFTs found');
  }

  console.log(`Found ${allAssets.length} compressed NFTs in wallet`);
  return allAssets;
}

async function getCNFTInfoByAssetId(assetId: string): Promise<any> {
  const umi = createUmi(HELIUS_RPC_URL).use(mplBubblegum()).use(dasApi());

  try {
    // Get asset from DAS API
    const asset = await umi.rpc.getAsset(publicKey(assetId));

    // Validate compression status
    if (!asset.compression?.compressed || asset.interface !== 'V1_NFT') {
      throw new Error('Not a valid compressed NFT');
    }

    // Extract core info
    const cnftInfo = {
      assetId: asset.id,
      owner: asset.ownership.owner,
      metadataUri: asset.content.json_uri,
      name: asset.content.metadata.name,
      symbol: asset.content.metadata.symbol,
      compressed: asset.compression.compressed,
      treeAddress: asset.compression.tree,
      leafIndex: asset.compression.leaf_id,
      content: asset.content,
    };

    return asset;
  } catch (error) {
    console.error('Error fetching cNFT:', error);
  }
}

async function getCNFTTransactionByAssetId(assetId: string) {
  try {
    let response: Record<any, any> = await fetch(HELIUS_RPC_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: 'text',
        jsonrpc: '2.0',
        method: 'getSignaturesForAsset',
        params: {
          id: assetId,
          page: 1,
          limit: 100,
        },
      }),
    });
    response = await response.json();
    if (response && response.result && response.result.items) {
      const transaction = response.result.items.find(
        ([, type]) => type === 'MintV1',
      );
      if (transaction) {
        const [signature] = transaction;
        return base58.serialize(signature) ?? null;
      }
    }
    return null;
  } catch (error) {
    console.error('Error getCNFTTransactionByAssetId:', error);
    return null;
  }
}

async function updateCNFT(req, linkMetadata) {
  console.log('CNFT Build');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // load the env variables and store the cluster RPC url
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  console.log('CLUSTER_URL');
  console.log(CLUSTER_URL);

  // define the address the tree will live at
  const umi = createUmi(CLUSTER_URL)
    .use(mplTokenMetadata())
    .use(mplBubblegum());

  // Create a keypair from your private key
  const payer = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  const wallet = await umi.eddsa.createKeypairFromSecretKey(
    new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
  );

  // const wallet = await loadKeypair(config.config_nft.wallet_keypair);
  // const payer = await loadKeypair(config.config_nft.nft_keypair);

  umi.use(keypairIdentity(payer));
  console.log('Payer address:', payer.publicKey);
  console.log('Wallet address:', wallet.publicKey);

  await getSolBalance();

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // get cNFT
  const assetId =
    '7picJzps1U4LZ6XTZsqVr22E1xubTzefKzR9VyFpcKri' as PublicKeyUmi;
  const assetWithProof = await getAssetWithProof(umi, assetId);
  // Then we can use it to update metadata for the NFT.
  const updateArgs = {
    uri: linkMetadata,
  };
  const result = await updateMetadata(umi, <
    UpdateMetadataInstructionAccounts & UpdateMetadataInstructionArgs
  >{
    ...assetWithProof,
    leafOwner: payer.publicKey,
    currentMetadata: {
      name: 'CA24 CAT3R #J-R01',
      uri: linkMetadata,
      sellerFeeBasisPoints: 100, // 1%
      collection: none(),
      creators: [
        { address: umi.identity.publicKey, verified: true, share: 100 },
      ],
    },
    updateArgs,
  }).sendAndConfirm(umi);
  console.log('RESULT UPDATE CNFT:', result);
  return true;
}

// tao cNFTs
// tao collection NFTs truoc - tao nhu collection NFT binh thuong
// tao cocurrent merkle tree
// mint compressed NFTs vào tree
async function buildTreeNftColections(req, linkMetadata) {
  // BUILD MERKLE TREE
  const merkleTree = await buildMerkleTree(req, linkMetadata);
  // BUILD COLLECTION
  // var collection_detail = await buildCollection(req,linkMetadata);
  // BUILD CNFT
  // var cnft_detail = await mintCNFT(req, linkMetadata);
  // UPDATE CNFT
  // var cnft_detail = await updateCNFT(req, linkMetadata);
  // GET LEAF
  // var leadID = await getLeafID(req, linkMetadata);
  // BURN CNFT
  // var burnDetail = await burnCNFT(req, linkMetadata);

  return true;
}

async function mintNFT(linkMetadata, name_nft, nft_symbol) {
  if (Common.isEmpty(linkMetadata)) return false;
  try {
    //////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////
    // get SOL balance
    await getSolBalance();

    //////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////
    // MINT NFT dùng cơ chế cũ metaplex foundation
    const connection = new Connection(clusterApiUrl('devnet'), 'confirmed');
    const keypair = Keypair.fromSecretKey(
      Buffer.from(JSON.parse(config.config_nft.nft_keypair)),
    );

    const metaplex = new Metaplex(connection);
    metaplex.use(keypairIdentity(keypair as any) as any);

    // const feePayerAirdropSignature = await connection.requestAirdrop(
    //   keypair.publicKey,
    //   LAMPORTS_PER_SOL
    // );
    // await connection.confirmTransaction(feePayerAirdropSignature);

    const mintNFTResponse = await metaplex.nfts().create(<CreateNftInput>{
      uri: linkMetadata,
      name: name_nft,
      symbol: nft_symbol,
      sellerFeeBasisPoints: 100,
      maxSupply: 1,
    });
    return mintNFTResponse;
  } catch (e) {
    console.log(e); // Logs the error
    // //tạo log body
    Common.logToFile('BODY-build-nft', e, fileNameLog);
  }
}

// async function mintNFTCore(linkMetadata, name_nft, nft_symbol) {
//   if (Common.isEmpty(linkMetadata)) return false;
//   try {
//     const CLUSTER_URL = config.config_nft.RPC_URL
//       ? config.config_nft.RPC_URL
//       : clusterApiUrl('devnet');
//     console.log('CLUSTER_URL');
//     console.log(CLUSTER_URL);
//
//     //////////////////////////////////////////////////////////////////////////////
//     //////////////////////////////////////////////////////////////////////////////
//     // get SOL balance
//     await getSolBalance();
//
//     //////////////////////////////////////////////////////////////////////////////
//     //////////////////////////////////////////////////////////////////////////////
//     // CONNECT BY UMI - MINT NFT bằng mpl core hay token metadata deu phai dung cai nay
//     // define the address the tree will live at
//     const umi = createUmi(CLUSTER_URL).use(mplCore());
//
//     // Create a keypair from your private key
//     const payer = await umi.eddsa.createKeypairFromSecretKey(
//       new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
//     );
//     // ADD PAYER VÀO UMI
//     umi.use(keypairIdentity(payer));
//
//     console.log('payer address:', payer.publicKey);
//
//     //////////////////////////////////////////////////////////////////////////////
//     // CREATE ACCOUNT PDA - TOKEN METADATA
//     // signer - assets - luu thong tin co ban cua owner va nft -- dung cua mpl token metadata
//     const mint = generateSigner(umi);
//     console.log('MINT: ');
//     console.log(mint);
//
//     //////////////////////////////////////////////////////////////////////////////
//     // MINT NFT - MPL CORE
//     // signer - assets - luu thong tin co ban cua owner va nft -- dung cua mpl core
//
//     const result = await createV1(umi, {
//       asset: mint, // dia chi nft
//       payer: payer,
//       name: name_nft,
//       uri: linkMetadata,
//     }).sendAndConfirm(umi);
//     console.log('NFT RESULT');
//     console.log(result);
//     return mint.publicKey;
//   } catch (e) {
//     console.log(e); // Logs the error
//     // //tạo log body
//     Common.logToFile('BODY-build-nft', e, fileNameLog);
//   }
// }

// async function mintNFTTokenMetadata(linkMetadata, name_nft, nft_symbol) {
//   if (Common.isEmpty(linkMetadata)) return false;
//   try {
//     const CLUSTER_URL = config.config_nft.RPC_URL
//       ? config.config_nft.RPC_URL
//       : clusterApiUrl('devnet');
//     console.log('CLUSTER_URL');
//     console.log(CLUSTER_URL);
//
//     //////////////////////////////////////////////////////////////////////////////
//     //////////////////////////////////////////////////////////////////////////////
//     // get SOL balance
//     await getSolBalance();
//
//     //////////////////////////////////////////////////////////////////////////////
//     //////////////////////////////////////////////////////////////////////////////
//     // CONNECT BY UMI - MINT NFT bằng mpl core hay token metadata deu phai dung cai nay
//     // define the address the tree will live at
//     const umi = createUmi(CLUSTER_URL).use(mplCore());
//
//     // Create a keypair from your private key
//     const payer = await umi.eddsa.createKeypairFromSecretKey(
//       new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
//     );
//     // ADD PAYER VÀO UMI
//     umi.use(keypairIdentity(payer));
//
//     console.log('payer address:', payer.publicKey);
//
//     //////////////////////////////////////////////////////////////////////////////
//     // CREATE ACCOUNT PDA - TOKEN METADATA
//     // signer - assets - luu thong tin co ban cua owner va nft -- dung cua mpl token metadata
//     const mint = generateSigner(umi);
//     console.log('MINT: ');
//     console.log(mint);
//
//     const account_pda = await createV1(umi, {
//       mint,
//       authority: payer,
//       name: name_nft,
//       uri: linkMetadata,
//       sellerFeeBasisPoints: percentAmount(1),
//       tokenStandard: TokenStandard.NonFungible,
//     }).sendAndConfirm(umi);
//     console.log('ACCOUNT PDA:');
//     console.log(account_pda);
//
//     //////////////////////////////////////////////////////////////////////////////
//     // MINT NFT - TOKEN METADATA
//
//     const result = await mintV1(umi, {
//       // có hàm này mới add nft vào ví được
//       mint: mint.publicKey,
//       authority: payer,
//       amount: 1,
//       tokenOwner: payer.publicKey,
//       tokenStandard: TokenStandard.NonFungible,
//     }).sendAndConfirm(umi);
//     console.log('NFT RESULT');
//     console.log(result);
//     return mint.publicKey;
//
//     return mintNFTResponse;
//   } catch (e) {
//     console.log(e); // Logs the error
//     // //tạo log body
//     Common.logToFile('BODY-build-nft', e, fileNameLog);
//   }
// }

// async function burnNFT() {
//   const CLUSTER_URL = config.config_nft.RPC_URL
//     ? config.config_nft.RPC_URL
//     : clusterApiUrl('devnet');
//   console.log('CLUSTER_URL');
//   console.log(CLUSTER_URL);
//
//   // define the address the tree will live at
//   const umi = createUmi(CLUSTER_URL).use(mplTokenMetadata());
//
//   //////////////////////////////////////////////////////////////////////////////
//   //////////////////////////////////////////////////////////////////////////////
//   // Create a keypair from your private key
//   const payer = await umi.eddsa.createKeypairFromSecretKey(
//     new Uint8Array(Buffer.from(JSON.parse(config.config_nft.nft_keypair))),
//   );
//   // ADD PAYER VÀO UMI
//   umi.use(keypairIdentity(payer));
//   console.log('payer address:', payer.publicKey);
//
//   //////////////////////////////////////////////////////////////////////////////
//   //////////////////////////////////////////////////////////////////////////////
//   // Create a keypair from your private key
//   const mint_keypair = await umi.eddsa.createKeypairFromSecretKey(
//     new Uint8Array(
//       Buffer.from(
//         JSON.parse(
//           '[83,88,252,78,106,31,150,159,157,103,13,16,57,212,33,237,131,4,156,230,151,146,10,154,143,189,35,138,221,102,78,97,3,98,124,146,137,163,87,185,2,88,170,214,167,100,153,202,34,68,31,196,137,144,32,20,128,254,187,188,4,93,38,237]',
//         ),
//       ),
//     ),
//   );
//   console.log('mint address:', mint_keypair.publicKey);
//
//   //////////////////////////////////////////////////////////////////////////////
//   //////////////////////////////////////////////////////////////////////////////
//   // get SOL balance
//
//   await getSolBalance();
//
//   //////////////////////////////////////////////////////////////////////////////
//   //////////////////////////////////////////////////////////////////////////////
//   // BURN NFT
//
//   const result = await burnV1(umi, {
//     mint: mint_keypair,
//     authority: payer,
//     tokenOwner: payer.publicKey,
//     tokenStandard: TokenStandard.NonFungible,
//   }).sendAndConfirm(umi);
//   console.log('BURN NFT RESULT');
//   console.log(result);
//
//   return true;
// }

async function getSolBalance() {
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // create a new rpc connection, using the ReadApi wrapper

  const connection = new Connection(CLUSTER_URL, 'confirmed');

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // get the payer's starting balance

  const initBalance = await connection.getBalance(
    new PublicKeyWeb3(config.config_nft.nft_pubkey),
  );
  console.log(
    'Account balance:',
    numberFormatter(initBalance / LAMPORTS_PER_SOL),
    'SOL\n',
  );
}

async function calcSolBuiltMerkleTree(maxDepth, maxBufferSize, canopyDepth) {
  const CLUSTER_URL = config.config_nft.RPC_URL
    ? config.config_nft.RPC_URL
    : clusterApiUrl('devnet');
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // create a new rpc connection, using the ReadApi wrapper

  const connection = new Connection(CLUSTER_URL, 'confirmed');

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  // calculate the space available in the tree
  const requiredSpace = getConcurrentMerkleTreeAccountSize(
    maxDepth,
    maxBufferSize,
    canopyDepth,
  );

  const storageCost =
    await connection.getMinimumBalanceForRentExemption(requiredSpace);

  // demonstrate data points for compressed NFTs
  console.log('Space to allocate:', numberFormatter(requiredSpace), 'bytes');
  console.log(
    'Estimated cost to allocate space:',
    numberFormatter(storageCost / LAMPORTS_PER_SOL),
  );
  console.log(
    'Max compressed NFTs for tree:',
    numberFormatter(Math.pow(2, maxDepth)),
    '\n',
  );

  // ensure the payer has enough balance to create the allocate the Merkle tree
  const initBalance = await connection.getBalance(
    new PublicKeyWeb3(config.config_nft.nft_pubkey),
  );
  if (initBalance < storageCost)
    return console.error('Not enough SOL to allocate the merkle tree');
  printConsoleSeparator('Not enough SOL to allocate the merkle tree');
}

async function generateWallet() {
  const walletKeyPair = Keypair.generate();

  console.log('Public Key:', walletKeyPair.publicKey.toString());
  console.log('Secret Key:', walletKeyPair.secretKey);
}

export default ActivityNft;
