import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  BelongsTo,
  HasMany,
  PrimaryKey,
} from 'sequelize-typescript';
import { Event } from '@events/models/event.model';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { User } from '@users/models/user.model';

@Table({
  tableName: 'user_booking_log',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class UserBookingLog extends Model<UserBookingLog> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  user_id: string;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @ForeignKey(() => EventStage)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Stage',
  })
  event_stage_id: number;

  @ForeignKey(() => EventStageDate)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event date',
  })
  event_stage_date_id: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
  })
  event_zone_detail_id: number[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Quantity | Seat',
  })
  seat_type: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  step: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  updated_at: Date;

  // Associations
  @BelongsTo(() => User, {
    foreignKey: 'user_id',
    as: 'user',
  })
  user: User;
}
