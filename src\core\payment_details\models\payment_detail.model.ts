import {
  Column,
  Model,
  Table,
  PrimaryKey,
  DataType,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from '@users/models/user.model';
import { Event } from '@events/models/event.model';
import { Transaction } from '@transactions/models/transaction.model';

@Table({
  tableName: 'payment_details',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class PaymentDetail extends Model<PaymentDetail> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @ForeignKey(() => Transaction)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  transaction_id: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user cập nhật từ table `users` sau khi được tạo ra.',
  })
  user_id: string;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user parent organizer',
  })
  event_id: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_stage_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  event_stage_date_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  event_zone_detail_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  code: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    defaultValue: 0,
  })
  coupon_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    defaultValue: 0,
  })
  combo_id: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
  })
  total_price: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
  })
  discounted_price: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  event_obj: object;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  user_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type_payment: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  payment_gateway: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  status_payment: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  status_sent_mail: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  status_mint_nft: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  date_use: number;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  // Associations
  @BelongsTo(() => User, {
    foreignKey: 'user_id',
    as: 'user',
  })
  user: User;
}
