import {
  Table,
  Column,
  Model,
  PrimaryKey,
  <PERSON>Type,
  <PERSON><PERSON>ult,
  <PERSON><PERSON>ey,
  BelongsTo,
} from 'sequelize-typescript';
import { Event } from '@events/models/event.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';

@Table({
  tableName: 'coupons',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class Coupon extends Model<Coupon> {
  @PrimaryKey
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Tên',
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Code',
  })
  code: string;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Giảm theo giá',
  })
  com_price: number;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '<PERSON><PERSON><PERSON><PERSON> theo %',
  })
  com_percent: number;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '<PERSON><PERSON> lượng Coupons',
  })
  quantity: number;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Số lượng Coupons đã sử dụng',
  })
  used: number;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '1 - không nhân số lượng | 2 - có nhân số lượng',
  })
  is_plural: number;

  @Default(1)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Ngưỡng cho phép để sử dụng coupon',
  })
  usage_threshold: number;

  @ForeignKey(() => EventStageDate)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event date',
  })
  event_date_id: number;

  @ForeignKey(() => Event)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  active: boolean;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Ngay su dung Coupons',
  })
  from_date: number;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Ngay het han Coupons',
  })
  to_date: number;

  @Default(false)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  deleted: boolean;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  created_at: Date;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;

  @Default(0)
  @Column({
    type: DataType.INTEGER,
    comment: 'orders',
  })
  orders: number;

  @BelongsTo(() => Event, { as: 'event' })
  event: Event;

  // Associations
  @BelongsTo(() => EventStageDate, {
    foreignKey: 'event_date_id',
    as: 'event_stage_date',
  })
  event_stage_date: EventStageDate;
}
