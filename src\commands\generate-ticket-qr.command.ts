import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

@Command({
  name: 'process-ticket-qr',
  description: 'Process generate transactions',
})
@Injectable()
export class GenerateTicketQrCommand extends CommandRunner {
  private readonly logger = new Logger(GenerateTicketQrCommand.name);
  private batchSize = 10; // Default batch size
  private timeDelayNextZone = 5 * 1000; // 5 seconds

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Thứ tự chạy:
  // B1: Sẽ tạo ra toàn bộ vé theo từng seat / zone / stage date / stage / event vào bảng cnft_temp
  // VD: npx ts-node src/cli.ts process-ticket-qr --event_id="4494915e-83ea-487d-99a9-d47ab727eab0"
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const limit = options?.batchSize || this.batchSize;
    const event_id = options?.event_id;
    console.info({
      limit,
      event_id,
    });

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    await firstValueFrom(
      this.httpService.post(
        `${app_domain}/_api/cnft/generate-ticket-qr-by-event-id`,
        { event_id: event_id },
        { headers },
      ),
    );
    console.info('<<<<<<<<<<<All tickets have been created.>>>>>>>>>>>>>');
  }

  @Option({
    flags: '-b, --batchSize <batchSize>',
    description: 'Batch size for processing',
  })
  parseBatchSize(val: string): number {
    return parseInt(val, this.batchSize);
  }

  @Option({
    flags: '-event_id, --event_id <event_id>',
  })
  parseEventId(val: string): string {
    return val;
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }
}
