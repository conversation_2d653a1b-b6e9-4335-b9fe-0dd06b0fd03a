export const PAGINATION = {
  LIMIT: 20,
  OFFSET: 0,
  PAGE: 1,
};
export const EVENTS = {
  IS_COMING_SOON: 1,
  IS_HOT: 1,
};
export const NEWS = {
  IS_HOT: 1,
};
export const DEFAULT_LANGUAGE = 'vi';

export const CACHE_TTL = {
  NON_EXPIRED: 0,
  TTL_LV1: 15 * 60, // 15 minutes
  TTL_LV2: 60 * 60, // 60 minutes
  TTL_LV3: 120 * 60, // 120 minutes
};

export const START_TRANSACTION_TIME = 15;
export const TYPE_PAYMENT = {
  SEAT: 'seat',
  QUANTITY: 'quantity',
};
export const BOOKING_MAX_QUANTITY = 4;
export const FREE_TICKET_PER_USER = 2;

export const TICKET_TYPE = {
  QR: 'qr',
  NFT: 'nft',
  PAPER: 'paper',
};
// Define ground event as category
export const EVENT_GROUP_DEFINED = {
  1: {
    slug: 'event',
    text: 'event',
  },
  2: {
    slug: 'workshop',
    text: 'workshop',
  },
  3: {
    slug: 'lgbt-show',
    text: 'lgbt_show',
  },
  4: {
    slug: 'sport',
    text: 'sport',
  },
  5: {
    slug: 'family-playground',
    text: 'family_playground',
  },
};

export const SIGNED_URL_EXPIRES = 60 * 60; // 1 hour
