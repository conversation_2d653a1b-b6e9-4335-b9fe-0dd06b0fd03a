import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { EventSeatPrice } from '@event_seat_prices/models/event_seat_price.model';

interface Attributes {
  [key: string]: any;
}

@Table({
  tableName: 'event_stage_date',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class EventStageDate extends Model<EventStageDate> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => EventStage)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Stage',
  })
  event_stage_id: number;

  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngày diễn ra sự kiện',
  })
  show_date: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Thời gian bắt đầu',
  })
  show_from: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Thời gian kết thúc',
  })
  show_to: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngày bán vé',
  })
  public_sale: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngày kết thúc bán vé',
  })
  public_sale_to: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả ngắn - VI',
  })
  short_desc_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả ngắn - EN',
  })
  short_desc_en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả giá - VI',
  })
  ticket_price_desc_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Mô tả giá - EN',
  })
  ticket_price_desc_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hình zone map',
  })
  zone_map: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hình zone price - VI',
  })
  zone_price_image_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hình zone price - EN',
  })
  zone_price_image_en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Phí dịch vụ',
  })
  service_fee: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  is_free_ticket: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: () => new Date(), // Use a function to set the default value to the current date
  })
  updated_at: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
    comment:
      'Check-in status: 1: chưa check in | 2: đang check in | 3: đã check in',
  })
  allow_check_in: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 3,
    comment: '1 - đang diễn ra | 2 - sắp diễn ra | 3 - đã diễn ra',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 1,
    comment: 'Show stage: 1 - show / 2 - hide',
  })
  show_stage: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Mã điểm bán',
  })
  salepoint_code: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 1,
    comment: 'Phân biệt loại vé: 1 = seat map map | 2 = quantity',
  })
  seat_type: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Phân biệt vé xuất ra: qr | nft | paper',
  })
  ticket_type: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Nội dung lưu ý hiển thị trong email sau khi đặt vé thành công',
  })
  email_note_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Nội dung lưu ý hiển thị trong email sau khi đặt vé thành công',
  })
  email_note_en: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  attributes: Attributes = {};

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  min_order: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 4,
  })
  max_order: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 5,
  })
  countdown: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
    comment:
      'Thời gian giới hạn của event: false: có giới hạn | true: không giới hạn',
  })
  is_unlimited: boolean;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment:
      'Email poster cho từng event date, trường hợp null, pdf vé sẽ lấy email poster từ table event',
  })
  email_poster_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment:
      'Email poster cho từng event date, trường hợp null, pdf vé sẽ lấy email poster từ table event',
  })
  email_poster_en: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  notify_email: string[];

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Nội dung chi tiết nếu muốn hiển thị',
  })
  content_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Nội dung chi tiết nếu muốn hiển thị',
  })
  content_en: string;

  // Associations
  @BelongsTo(() => EventStage, {
    foreignKey: 'event_stage_id',
    as: 'event_stage',
  })
  event_stage: EventStage;

  @HasMany(() => EventZoneDetail, {
    foreignKey: 'event_date_id',
    as: 'event_zone_detail',
  })
  event_zone_detail: EventZoneDetail[];
}
