import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { EventStage } from './models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';

@Injectable()
export class EventStagesService {
  constructor(
    @InjectModel(EventStage)
    private readonly eventStageModel: typeof EventStage,
    private sequelize: Sequelize,
  ) {}

  async getEventStageDetail(event_stage_id, includes = false) {
    const query: any = {
      where: {
        active: true,
        deleted: false,
        id: event_stage_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    };

    if (includes) {
      query.include = [
        {
          model: EventStageDate,
          where: { active: true, deleted: false },
          as: 'event_stage_date',
        },
      ];
    }

    return await this.eventStageModel.findOne(query);
  }
}
