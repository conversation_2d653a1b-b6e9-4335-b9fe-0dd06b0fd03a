import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Coupon } from './models/coupon.model';

@Injectable()
export class CouponsService {
  constructor(
    @InjectModel(Coupon)
    private readonly couponModel: typeof Coupon,
    private sequelize: Sequelize,
  ) {}

  async getCouponDetail(coupon_id) {
    return await this.couponModel.findOne({
      where: {
        id: coupon_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- <PERSON> is here
    });
  }
}
