import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import RestAPI from 'common/rest_api';
import { TempSeat } from './models/temp_seat.model';
import { User } from '@users/models/user.model';
import { EventStageDatesService } from '@event_stage_dates/event_stage_dates.services';
import _ from 'lodash';
import { Op } from 'sequelize';
@Injectable()
export class TempSeatsService {
  constructor(
    @InjectModel(TempSeat)
    private readonly tempSeatModel: typeof TempSeat,

    private eventStageDatesService: EventStageDatesService,

    private sequelize: Sequelize,
  ) {}

  async getListTempSeatByTransactionId(transaction_id, date_use = 0) {
    const condition = {
      transaction_id,
      mint_address: {
        [Op.is]: null,
      },
    };
    if (date_use && date_use > 0) {
      _.assign(condition, { date_use: date_use });
    }

    return await this.tempSeatModel.findAll({
      where: condition,
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  async getListTempSeat(conditions, includes = false) {
    const query: any = {
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    };

    if (includes) {
      query.include = [
        {
          model: User,
          as: 'user',
          required: true,
        },
      ];
    }

    return await this.tempSeatModel.findAll(query);
  }

  async getTempSeatDetail(conditions: Partial<TempSeat>) {
    return await this.tempSeatModel.findOne({
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async create(payload: any, res: Response): Promise<any> {
    const t = await this.sequelize.transaction();
    try {
      // Kiểm tra min,max order theo event date
      const eventStageDateDetail =
        await this.eventStageDatesService.getEventStageDateDetail(
          payload.event_stage_date_id,
        );
      if (!eventStageDateDetail) {
        t.commit();
        return RestAPI.success(res, {
          code: 'EVENT_DATE_NOT_EXIST',
        });
      }
      const countRecord = await this.tempSeatModel.count({
        where: {
          event_id: payload.event_id,
          transaction_id: payload.transaction_id,
          user_id: payload.user_id,
        },
        transaction: t,
      });

      if (countRecord >= eventStageDateDetail.max_order) {
        t.commit();
        return RestAPI.success(res, {
          code: 'BOOK_SEAT_TO_MAX',
        });
      }

      const existingRecord = await this.tempSeatModel.findOne({
        where: {
          event_id: payload.event_id,
          event_stage_id: payload.event_stage_id,
          event_stage_date_id: payload.event_stage_date_id,
          event_zone_detail_id: payload.event_zone_detail_id,
          event_seat_id: payload.event_seat_id,
          seat_number: payload.seat_number,
          date_use: payload?.date_use ?? 0,
          seat_code: payload.seat_code,
        },
        transaction: t,
      });

      if (existingRecord) {
        t.commit();
        return RestAPI.success(res, {
          code: 'UNAVAIBLE_SEAT',
        });
      } else {
        const createdData = await this.tempSeatModel.create(payload, {
          transaction: t,
        });
        t.commit();
        return RestAPI.success(res, {
          code: 'BOOK_SEAT_SUCCESS',
          ...createdData.dataValues,
        });
      }
    } catch (error) {
      t.rollback();
      return RestAPI.badRequest(res, error);
    }
  }

  async removeTempSeat(payload: any): Promise<any> {
    const t = await this.sequelize.transaction();
    try {
      const existingRecord = await this.tempSeatModel.findOne({
        where: {
          transaction_id: payload.transaction_id,
          is_invoice: false,
        },
        transaction: t,
      });
      existingRecord && (await existingRecord.destroy({ transaction: t }));
      await t.commit();
    } catch (error) {
      t.rollback();
    }
  }

  async removeTempSeatByCondition(condtion: any): Promise<any> {
    const t = await this.sequelize.transaction();
    try {
      const existingRecord = await this.tempSeatModel.findOne({
        where: {
          ...condtion,
          is_invoice: false,
        },
        transaction: t,
      });
      existingRecord && (await existingRecord.destroy({ transaction: t }));
      await t.commit();
    } catch (error) {
      t.rollback();
    }
  }

  async removeTempSeatByTransactionIds(transaction_ids: any): Promise<any> {
    const t = await this.sequelize.transaction();
    try {
      await this.tempSeatModel.destroy({
        where: {
          transaction_id: transaction_ids,
          is_invoice: false,
        },
        transaction: t,
      });
      await t.commit();
    } catch (error) {
      t.rollback();
    }
  }
}
