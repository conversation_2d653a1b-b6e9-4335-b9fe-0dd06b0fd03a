import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ParticipantInfoService } from './participants_info.services';
import { ParticipantInfo } from './models/participants_info.model';

@Module({
  imports: [SequelizeModule.forFeature([ParticipantInfo])],
  providers: [ParticipantInfoService],
  exports: [ParticipantInfoService],
})
export class ParticipantInfoModule {}
