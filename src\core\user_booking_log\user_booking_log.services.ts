import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserBookingLog } from './models/user_booking_log.model';
import { User } from '@users/models/user.model';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { EventsService } from '@events/events.services';
import { EventStagesService } from '@event_stages/event_stages.services';
import { EventStageDatesService } from '@event_stage_dates/event_stage_dates.services';
import { EventZoneDetailsService } from '@event_zone_details/event_zone_details.services';
import { EventSeatPricesService } from '@event_seat_prices/event_seat_prices.services';
import { Event } from '@events/models/event.model';
import _ from 'lodash';
import Common from 'common/common';
import Constants from 'common/constants';
import moment from 'moment';

@Injectable()
export class UserBookingLogService {
  constructor(
    @InjectModel(UserBookingLog)
    private readonly userBookingLogModel: typeof UserBookingLog,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Event)
    private readonly eventModel: typeof Event,
    @InjectModel(EventStage)
    private readonly eventStageModel: typeof EventStage,
    @InjectModel(EventStageDate)
    private readonly eventStageDateModel: typeof EventStageDate,
    @InjectModel(EventZoneDetail)
    private readonly eventZoneDetailModel: typeof EventZoneDetail,

    private eventService: EventsService,
    private eventStageService: EventStagesService,
    private eventStageDateService: EventStageDatesService,
    private eventZoneDetailService: EventZoneDetailsService,
    private eventSeatPricesService: EventSeatPricesService,
  ) {}

  async getByEventID(params: any) {
    const { event_id, limit } = params;

    if (!event_id || _.isEmpty(event_id) || _.isEmpty(event_id)) {
      return [];
    } else {
      const data = await this.eventModel
        .findOne({
          where: {
            id: event_id,
            active: true,
            deleted: false,
          },
          attributes: [
            'id',
            ['attributes', 'attributes_event'],
            'showdate_from',
            'event_group_id',
            ['name_vi', 'name'],
            ['slug_vi', 'slug'],
            ['venue_vi', 'venue'],
            ['poster_vi', 'poster'],
            ['banner_desktop_vi', 'banner_desktop'],
            ['desc_unlimited_vi', 'desc_unlimited'],
            'is_unlimited',
          ],
          raw: true,
          nest: true,
        })
        .then(async (eventDetail: any) => {
          if (!eventDetail || _.isEmpty(eventDetail)) return [];

          const innerQuery: any = {
            where: { event_id: event_id, step: 1 },
            include: [
              {
                model: User,
                as: 'user',
              },
            ],
            raw: true,
            nest: true,
          };

          if (limit && limit !== undefined) {
            _.assign(innerQuery, { limit: parseInt(limit) });
          }

          //
          // Tìm tất cả user đã được log ở step 1
          return await this.userBookingLogModel
            .findAll(innerQuery)
            .then(async (dataBookingLog) => {
              if (!dataBookingLog || _.isEmpty(dataBookingLog)) return [];

              const listDataSendEmail: any = [];
              const listAvoidDuplicateEmail: any = [];
              for (let i = 0; i < dataBookingLog.length; i++) {
                const itemLog = dataBookingLog[i];

                if (_.includes(listAvoidDuplicateEmail, itemLog.user.email)) {
                  continue;
                }

                if (
                  _.isEmpty(itemLog.user.email) ||
                  _.isNil(itemLog.user.email) ||
                  _.isEmpty(itemLog.user.name) ||
                  _.isNil(itemLog.user.name) ||
                  _.isEmpty(itemLog.user.last_name) ||
                  _.isNil(itemLog.user.last_name)
                ) {
                  continue;
                }

                listAvoidDuplicateEmail.push(itemLog.user.email);

                const showdate_from = eventDetail?.is_unlimited
                  ? moment().tz(Constants.DEFAULT_TIMEZONE).format('X')
                  : eventDetail.showdate_from;
                const emailData = {
                  show_from: eventDetail.desc_unlimited
                    ? eventDetail.desc_unlimited
                    : `${_.capitalize(
                        Common.getDateFormat(
                          showdate_from,
                          Constants.DAY_OF_WEEK_VI,
                        ),
                      )} ${eventDetail?.attributes_event?.show_hour == undefined || eventDetail?.attributes_event?.show_hour ? _.toUpper(Common.getDateFormat(showdate_from, 'H:mm')) : ''}`,
                  poster: `https://vtix.vn${Common.renderFullPathImageTicket(
                    eventDetail,
                    eventDetail.banner_desktop,
                  )}`,
                  venue: eventDetail.venue,
                  name: eventDetail.name,
                  email: itemLog.user.email,
                  phone: itemLog.user.phone,
                  user_name: `${itemLog.user.last_name} ${itemLog.user.name}`,
                  link: `https://vtix.vn/vi${Common.renderFullPathImageTicket(
                    eventDetail,
                    eventDetail.slug,
                  )}`,
                };
                listDataSendEmail.push(emailData);
              }

              return listDataSendEmail;
            });
          // Kết thúc Tìm tất cả user đã được log ở step 1
        });

      return data;
    }
  }
}
