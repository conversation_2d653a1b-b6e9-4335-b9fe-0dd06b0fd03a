import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Event } from '@events/models/event.model';
import { EventsService } from './events.services';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { EventsController } from './events.controller';
import { EventStage } from '@event_stages/models/event_stage.model';
import { User } from '@users/models/user.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventSeatPrice } from '@event_seat_prices/models/event_seat_price.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Event,
      EventZoneDetail,
      EventStage,
      User,
      EventStageDate,
      EventSeatPrice,
    ]),
  ],
  controllers: [EventsController],
  providers: [EventsService],
  exports: [EventsService],
})
export class EventsModule {}
