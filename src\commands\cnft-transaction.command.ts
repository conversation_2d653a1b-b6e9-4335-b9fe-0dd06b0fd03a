import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

@Command({
  name: 'process-transactions',
  description: 'Process generate transactions',
})
@Injectable()
export class CnftTransactionCommand extends CommandRunner {
  private readonly logger = new Logger(CnftTransactionCommand.name);
  private batchSize = 1; // 10; // Default batch size
  private timeDelayCallTransaction = 5 * 60 * 1000; // 5 minutes
  private timeDelayNextZone = 2 * 60 * 1000; // 2 minutes
  private isProcessingComplete = false;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Thứ tự chạy:
  // B1: Sẽ tạo ra toàn bộ vé theo từng seat / zone / stage date / stage / event vào bảng cnft_temp
  // B2: Sẽ lấy transation, limit mỗi lần chạy là 10 và deplay 5 phút
  // VD: npx ts-node src/cli.ts process-transactions --event_id="a42f0c9c-7dbd-4f58-ad14-adb78c98168f" --event_stage_id=8 --event_date_id=18
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const limit = options?.batchSize || this.batchSize;
    const event_id = options?.event_id;
    const event_stage_id = options?.event_stage_id;
    const event_date_id = options?.event_date_id;
    const event_zone_detail_id = options?.event_zone_detail_id;

    console.info({
      limit,
      event_id,
      event_stage_id,
      event_date_id,
      event_zone_detail_id,
    });

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    const getEventZoneIds = await firstValueFrom(
      this.httpService.get(
        `${app_domain}/_api/events/get-list-event-date-by-zone?event_date_id=${event_date_id}`,
        { headers },
      ),
    );
    const listEventZoneIds = getEventZoneIds.data.data || [];
    if (listEventZoneIds.length === 0) {
      console.info('No event zone found');
      return;
    }

    for (const eventZoneId of listEventZoneIds) {
      const payload = {
        event_id,
        event_stage_id,
        event_date_id,
        event_zone_detail_id: eventZoneId,
      };

      const generateTicketsByZone = await firstValueFrom(
        this.httpService.post(
          `${app_domain}/_api/cnft/generate-ticket-by-zone`,
          payload,
          { headers },
        ),
      );

      console.info(
        '--------Generate ticket success---------',
        generateTicketsByZone.data.data.total,
      );
      const getTotalTransactionRemain = await firstValueFrom(
        this.httpService.post(
          `${app_domain}/_api/cnft/total-transaction-remain`,
          payload,
          { headers },
        ),
      );
      const totalTransactionRemain =
        getTotalTransactionRemain.data.data.total ?? 0;
      if (totalTransactionRemain === 0) {
        console.info('No transaction remain');
        continue;
      }
      const callNumber = Math.ceil(totalTransactionRemain / limit);
      console.info(
        `Starting generate transactions with Zone: ${eventZoneId} - total: ${totalTransactionRemain} - call: ${callNumber} times`,
      );
      for (let i = 1; i <= callNumber; i++) {
        console.info(
          `----Call ${i} time with ${JSON.stringify({ ...payload, limit: limit })}`,
        );
        const transactions = await firstValueFrom(
          this.httpService.post(
            `${app_domain}/_api/cnft/generate-cnft-by-zone`,
            { ...payload, limit: limit },
            { headers },
          ),
        );
        console.info(
          '--------Generate success - total: ',
          transactions.data.data.total,
        );
        await this.delayTime(this.timeDelayCallTransaction);
      }
      await this.delayTime(this.timeDelayNextZone);
    }
    console.info('<<<<<<<<<<<All transactions have been created.>>>>>>>>>>>>>');
  }

  @Option({
    flags: '-b, --batchSize <batchSize>',
    description: 'Batch size for processing',
  })
  parseBatchSize(val: string): number {
    return parseInt(val, this.batchSize);
  }

  @Option({
    flags: '-event_id, --event_id <event_id>',
  })
  parseEventId(val: string): string {
    return val;
  }

  @Option({
    flags: '-event_stage_id, --event_stage_id <event_stage_id>',
  })
  parseEventStageId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_date_id, --event_date_id <event_date_id>',
  })
  parseEventDateId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_zone_detail_id, --event_zone_detail_id <event_date_id>',
  })
  parseEventZoneDetailId(val: string): number {
    return parseInt(val);
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }
}
