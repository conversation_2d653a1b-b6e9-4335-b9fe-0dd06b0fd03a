import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TempQuantity } from './models/temp_quantity.model';
import { TempQuantitiesService } from './temp_quantities.services';
import { EventSeatPricesModule } from '@event_seat_prices/event_seat_prices.module';
import { EventStageDatesModule } from '@event_stage_dates/event_stage_dates.module';
import { EventZoneDetailsModule } from '@event_zone_details/event_zone_details.module';

@Module({
  imports: [
    SequelizeModule.forFeature([TempQuantity]),
    EventSeatPricesModule,
    EventStageDatesModule,
    EventZoneDetailsModule,
  ],
  providers: [TempQuantitiesService],
  exports: [TempQuantitiesService],
})
export class TempQuantitiesModule {}
