import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Event } from '@events/models/event.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import _ from 'lodash';

@Injectable()
export class EventsService {
  constructor(
    @InjectModel(Event)
    private readonly eventModel: typeof Event,
    @InjectModel(EventZoneDetail)
    private readonly eventZoneDetailModel: typeof EventZoneDetail,
  ) {}
  async getEventDetail(event_id) {
    return await this.eventModel.findOne({
      where: {
        active: true,
        deleted: false,
        id: event_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }
  async getListEventDateByZone(params) {
    const { event_date_id } = params;
    const listEventDateIds = await this.eventZoneDetailModel.findAll({
      where: {
        active: true,
        deleted: false,
        event_date_id: parseInt(event_date_id),
      },
      attributes: ['id', 'event_date_id'],
      order: [['id', 'ASC']],
      raw: true, // <----------- Magic is here
    });
    return listEventDateIds.map((val) => val.id);
  }
}
