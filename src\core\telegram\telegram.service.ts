import { Injectable, OnModuleInit } from '@nestjs/common';
import { Telegraf } from 'telegraf';

@Injectable()
export class TelegramService implements OnModuleInit {
  private bot: Telegraf;
  private readonly botToken =
    process.env.TELEGRAM_BOT_TOKEN ??
    '**********************************************';
  private readonly groupId = process.env.TELEGRAM_CHAT_ID ?? '-4682580682';

  onModuleInit() {
    // Initialize the bot
    this.bot = new Telegraf(this.botToken);

    this.bot
      .launch()
      .then(() => console.log('Bot started with polling'))
      .catch((error) => console.log('Failed to start telegram bot'));
  }

  async sendMessageToGroup(
    chatId: string = this.groupId,
    message: string,
  ): Promise<void> {
    try {
      // await this.bot.sendMessage(this.groupId, message);
      await this.bot.telegram.sendMessage(chatId, message);
      console.log('Message sent successfully to the group');
    } catch (error) {
      console.error('Failed to send message: sendMessageToGroup >> ', error);
    }
  }

  async sendExcelAttach(
    chatId: string,
    fileBuffer: any,
    fileName: string = 'data',
  ) {
    try {
      // Send the Excel buffer as a document
      await this.bot.telegram.sendDocument(chatId, {
        source: fileBuffer,
        filename: `${fileName}.xlsx`,
      });

      console.log('Excel file sent successfully!');
    } catch (error) {
      console.error('Error sending Excel file:', error);
    }
  }

  async sendPaymentNotification(data: {
    eventName: string;
    orderCode: string;
    seats: string[];
    totalPrice: string;
    statusPayment: boolean;
    userName: string;
    phone: string;
    email: string;
  }): Promise<void> {
    const formatPrice = Number(data.totalPrice).toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
    const message = `---- ${data.eventName} ----
Mã đơn hàng: ${data.orderCode}
Tên khách hàng: ${data.userName}
Sđt: ${data.phone}
Email: ${data.email}
Số ghế: ${data.seats.join(', ')}
Tổng đơn hàng: ${formatPrice}
Trạng thái thanh toán: ${data.statusPayment ? 'Đã thanh toán' : 'N/A'}`;

    await this.sendMessageToGroup(this.groupId, message);
  }
}
