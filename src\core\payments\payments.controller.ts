import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Patch,
  UseGuards,
  Req,
  Res,
  Query,
  Request,
} from '@nestjs/common';
import { PaymentsService } from './payments.services';
import _ from 'lodash';
import RestAPI from 'common/rest_api';
import { InternalAPIGuard } from 'guards/internalAPI.guard';

@Controller('')
export class PaymentsController {
  constructor(private readonly paymentService: PaymentsService) {}

  @Get('/payments/remind-booking')
  @UseGuards(InternalAPIGuard)
  async getAllPaymentDetailWithEventIDNotPaid(
    @Query()
    params: { event_id: string; limit: number },
    @Res() res: Response,
  ): Promise<any> {
    const listData =
      await this.paymentService.getAllPaymentDetailWithEventIDNotPaid(params);
    return RestAPI.success(res, listData);
  }
}
