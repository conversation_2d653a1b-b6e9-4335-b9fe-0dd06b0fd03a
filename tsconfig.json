{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./src",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": true,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "esModuleInterop": true,
    "paths": {
      "@accounts/*": [
        "core/accounts/*"
      ],
      "@users/*": [
        "core/users/*"
      ],
      "@cart_temps/*": [
        "core/cart_temps/*"
      ],
      "@cart_detail_temps/*": [
        "core/cart_detail_temps/*"
      ],
      "@categories/*": [
        "core/categories/*"
      ],
      "@category_langs/*": [
        "core/category_langs/*"
      ],
      "@cnft_temps/*": [
        "core/cnft_temps/*"
      ],
      "@combos/*": [
        "core/combos/*"
      ],
      "@events/*": [
        "core/events/*"
      ],
      "@event_stages/*": [
        "core/event_stages/*"
      ],
      "@event_stage_dates/*": [
        "core/event_stage_dates/*"
      ],
      "@event_zone_details/*": [
        "core/event_zone_details/*"
      ],
      "@event_seat_prices/*": [
        "core/event_seat_prices/*"
      ],
      "@email_attendees/*": [
        "core/email_attendees/*"
      ],
      "@custom_tickets/*": [
        "core/custom_tickets/*"
      ],
      "@countries/*": [
        "core/countries/*"
      ],
      "@coupons/*": [
        "core/coupons/*"
      ],
      "@history_payment_onlines/*": [
        "core/history_payment_onlines/*"
      ],
      "@history_payments/*": [
        "core/history_payments/*"
      ],
      "@post_langs/*": [
        "core/post_langs/*"
      ],
      "@posts/*": [
        "core/posts/*"
      ],
      "@states/*": [
        "core/states/*"
      ],
      "@transactions/*": [
        "core/transactions/*"
      ],
      "@temp_seats/*": [
        "core/temp_seats/*"
      ],
      "@temp_quantities/*": [
        "core/temp_quantities/*"
      ],
      "@payment_details/*": [
        "core/payment_details/*"
      ],
      "@participants_info/*": [
        "core/participants_info/*"
      ],
      "@popups/*": [
        "core/popups/*"
      ],
      "@voucher_providers/*": [
        "core/voucher_providers/*"
      ],
      "@vouchers/*": [
        "core/vouchers/*"
      ],
      "@user_has_vouchers/*": [
        "core/user_has_vouchers/*"
      ],
    }
  },
  "include": [
    "src/**/*",
    "node_modules/@types/node/buffer.d.ts",
    "node_modules/@types/node/buffer.d.ts"
  ],
  "exclude": [
    "node_modules",
    "**/*.spec.ts"
  ]
}