import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Transaction } from './models/transaction.model';
import { TransactionsService } from './transactions.services';
import { Event } from '@events/models/event.model';
import { User } from '@users/models/user.model';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { EventSeatPrice } from '@event_seat_prices/models/event_seat_price.model';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import { CouponsModule } from '@coupons/coupons.module';
import { ParticipantInfoModule } from '@participants_info/participants_info.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Transaction,
      Event,
      User,
      EventStage,
      EventStageDate,
      EventZoneDetail,
      EventSeatPrice,
      PaymentDetail,
    ]),
    CouponsModule,
    ParticipantInfoModule,
  ],
  providers: [TransactionsService],
  exports: [TransactionsService],
})
export class TransactionsModule {}
