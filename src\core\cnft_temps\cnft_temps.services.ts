import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CNftTemp } from './models/cnft_temp.model';
import { EventZoneDetailsService } from '@event_zone_details/event_zone_details.services';
import { EventSeatPricesService } from '@event_seat_prices/event_seat_prices.services';
import RestAPI from 'common/rest_api';
import Constants from 'common/constants';
import Common from 'common/common';
import { Op } from 'sequelize';
import { EventsService } from '@events/events.services';
import { EventStageDatesService } from '@event_stage_dates/event_stage_dates.services';
import ActivityNft from 'common/activity_nft';
import { TICKET_TYPE } from 'config/constant';
import _ from 'lodash';
import { StoragesService } from 'storages/storages.service';
import { Bucket } from 'utils/buckets';

@Injectable()
export class CNftTempsService {
  constructor(
    @InjectModel(CNftTemp)
    private readonly cNftModel: typeof CNftTemp,

    private eventZoneDetailService: EventZoneDetailsService,
    private eventSeatPriceService: EventSeatPricesService,
    private eventService: EventsService,
    private eventStageDateService: EventStageDatesService,
    private storagesService: StoragesService,
  ) {}

  async generateTicketsByZone(payload, res) {
    const event_id = payload.event_id;
    const event_stage_id = payload.event_stage_id;
    const event_date_id = payload.event_date_id;
    const event_zone_detail_id = payload.event_zone_detail_id;
    const event_zone_detail =
      await this.eventZoneDetailService.getEventZoneDetail(
        event_zone_detail_id,
      );
    if (event_zone_detail) {
      if (
        event_zone_detail.event_id == event_id &&
        event_zone_detail.event_stage_id == event_stage_id &&
        event_zone_detail.event_date_id == event_date_id
      ) {
        const event_seat_price =
          await this.eventSeatPriceService.getListEventSeatPrice(
            event_zone_detail_id,
          );
        if (event_seat_price) {
          const cnft_inserts: any = [];
          for (
            let iSeatPrice = 0;
            iSeatPrice < event_seat_price.length;
            iSeatPrice++
          ) {
            const item = event_seat_price[iSeatPrice];
            if (item.total_seat_row > 0 && !_.isEmpty(item.seat_position)) {
              const seatPosition = Object.values(item.seat_position);

              for (let i = 0; i < seatPosition.length; i++) {
                const pos = seatPosition[i];

                const seat_name = pos < 10 ? '0' + pos : pos;

                let convertFullSeatName = seat_name;
                if (
                  item.row_code &&
                  !_.isUndefined(item.row_code) &&
                  !_.isEmpty(item.row_code)
                ) {
                  convertFullSeatName = `${item.row_code}-${seat_name}`;
                }

                // Bắt đầu insert từng vé vào nft temp table
                cnft_inserts.push({
                  event_id: event_zone_detail.event_id,
                  event_stage_id: event_zone_detail.event_stage_id,
                  event_date_id: event_zone_detail.event_date_id,
                  event_zone_detail_id: event_zone_detail_id,
                  custom_ticket_id: 0,
                  event_seat_id: item.id,
                  seat_name: convertFullSeatName,
                  pos: pos,
                  row_code: item.row_code,
                });
                // Kết thúc insert
              }
            }
          }

          // Tìm xem đã có vé tồn tại trong nft temp table chưa
          return await this.cNftModel
            .findAll({
              where: {
                event_id: event_zone_detail.event_id,
                event_stage_id: event_zone_detail.event_stage_id,
                event_date_id: event_zone_detail.event_date_id,
                event_zone_detail_id: event_zone_detail_id,
              },
              raw: true,
              nest: true,
            })
            .then(async (resFindAll) => {
              const cnft_unique: any = [];
              for (
                let i_cnft_insert = 0;
                i_cnft_insert < cnft_inserts.length;
                i_cnft_insert++
              ) {
                const element = cnft_inserts[i_cnft_insert];
                const findExistNft = _.find(resFindAll, {
                  event_id: element.event_id,
                  event_stage_id: element.event_stage_id,
                  event_date_id: element.event_date_id,
                  event_zone_detail_id: element.event_zone_detail_id,
                  event_seat_id: element.event_seat_id,
                  seat_name: element.seat_name,
                });
                if (!findExistNft) {
                  cnft_unique.push(element);
                }
              }

              let dataInserted: any = '';
              if (cnft_unique.length > 0) {
                try {
                  dataInserted = await this.cNftModel.bulkCreate(cnft_unique, {
                    ignoreDuplicates: true,
                  });
                  return RestAPI.success(res, {
                    dataInserted: dataInserted,
                    total: cnft_unique.length,
                  });
                } catch (error) {
                  return RestAPI.success(res, {
                    dataInserted: [],
                    total: cnft_unique.length,
                  });
                }
              } else {
                return RestAPI.success(res, {
                  dataInserted: [],
                  total: cnft_unique.length,
                });
              }
            });
          // Kết thúc tìm kiếm
        } else {
          return RestAPI.badRequest(
            res,
            Constants.MSG.EVENT_SEAT_PRICE_IS_NOT_EXISTED_ERR,
          );
        }
      } else {
        return RestAPI.badRequest(res, Constants.MSG.EVENT_IS_NOT_EXISTED_ERR);
      }
    } else {
      return RestAPI.badRequest(
        res,
        Constants.MSG.EVENT_ZONE_DETAIL_IS_NOT_EXISTED_ERR,
      );
    }
  }

  async getCnftTemps(where_array, limit, offset = 0) {
    if (Common.isEmpty(limit)) limit = 3;
    const Obj: any = {
      offset: offset,
      limit: limit,
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    };
    if (!Common.isEmpty(where_array)) Obj.where = where_array;
    return await this.cNftModel.findAll(Obj);
  }

  async getTotalCNFTTemp(conditions, res) {
    const total = await this.cNftModel.count({
      where: {
        ...conditions,
        deleted: false,
      },
    });
    return RestAPI.success(res, {
      total,
    });
  }

  async getTotalTransactionRemain(conditions, res) {
    const total = await this.cNftModel.count({
      where: {
        ...conditions,
        deleted: false,
        transaction: {
          [Op.is]: null,
        },
      },
    });
    return RestAPI.success(res, {
      total,
    });
  }

  async getTotalMintAddressRemain(conditions, res) {
    const total = await this.cNftModel.count({
      where: {
        ...conditions,
        deleted: false,
        // transaction: {
        //   [Op.not]: null,
        // },
        // mint_address: {
        //   [Op.is]: null,
        // },
        [Op.or]: [
          {
            mint_address: {
              [Op.is]: null,
            },
          },
          { mint_address: '' },
        ],
      },
    });
    return RestAPI.success(res, {
      total,
    });
  }

  async generateCNFTByTickets(req, payload, res) {
    const event_id = payload.event_id;
    const event_stage_id = payload.event_stage_id;
    const event_date_id = payload.event_date_id;
    const event_zone_detail_id = payload.event_zone_detail_id;
    const limit = payload.limit || 10;

    const tickets = await this.getCnftTemps(
      {
        deleted: false,
        event_id: event_id,
        event_stage_id: event_stage_id,
        event_date_id: event_date_id,
        event_zone_detail_id: event_zone_detail_id,
        transaction: {
          [Op.is]: null,
        },
      },
      limit,
    ); // limit 10
    console.log('tickets :>> ', tickets.length);
    // return RestAPI.success(res, tickets);
    const event_detail = await this.eventService.getEventDetail(event_id);
    const event_stage_date_detail =
      await this.eventStageDateService.getEventStageDateDetail(event_date_id);
    const event_zone_detail =
      await this.eventZoneDetailService.getEventZoneDetail(
        event_zone_detail_id,
      );
    if (event_detail) {
      tickets.forEach(async (element) => {
        // const prefixCode = Common.firstCodeGIFT() + event_detail.code;
        const prefixCode = event_detail.code;
        const code_order = prefixCode + Common.getRandomCode(10); // 13 ki tu

        // build cNFT
        const { path, attribute_nft, nft_name, file_name } =
          await ActivityNft.convertCNFTTempData(
            req,
            element,
            code_order,
            event_zone_detail,
            event_detail,
            event_stage_date_detail,
          );
        const linkMetadata = await this.createJsonFileToS3(
          path,
          attribute_nft,
          file_name,
        );
        const cnft_transaction = await ActivityNft.buildCNFTTempv2(
          linkMetadata,
          nft_name,
          element,
        );
        console.log('cnft_transaction :>> ', cnft_transaction);

        await this.cNftModel.update(
          {
            transaction: cnft_transaction.toString(),
            code_order: code_order,
          },
          {
            where: { id: element.id },
          },
        );
      });
      return RestAPI.success(res, {
        total: tickets.length,
        tickets: tickets,
      });
    } else {
      return RestAPI.badRequest(res, Constants.MSG.EVENT_IS_NOT_EXISTED_ERR);
    }
  }

  async updateCNFTJsonByTicket(req, payload, res) {
    const event_id = payload.event_id;
    const event_stage_id = payload.event_stage_id;
    const event_date_id = payload.event_date_id;
    const event_zone_detail_id = payload.event_zone_detail_id;
    const limit = payload.limit || 10;

    const tickets = await this.getCnftTemps(
      {
        deleted: false,
        event_id: event_id,
        event_stage_id: event_stage_id,
        event_date_id: event_date_id,
        event_zone_detail_id: event_zone_detail_id,
      },
      limit,
    ); // limit 10
    console.log('tickets :>> ', tickets.length);
    // return RestAPI.success(res, tickets);
    const event_detail = await this.eventService.getEventDetail(event_id);
    const event_stage_date_detail =
      await this.eventStageDateService.getEventStageDateDetail(event_date_id);
    const event_zone_detail =
      await this.eventZoneDetailService.getEventZoneDetail(
        event_zone_detail_id,
      );
    if (event_detail) {
      tickets.forEach(async (element) => {
        const code_order = element.code_order;

        // build cNFT
        const { path, attribute_nft, file_name } =
          await ActivityNft.convertCNFTTempData(
            req,
            element,
            code_order,
            event_zone_detail,
            event_detail,
            event_stage_date_detail,
          );

        // Update cnft json file to s3
        console.log('>>> path :>> ', path);
        console.log('>>> file_name :>> ', file_name);
        await this.createJsonFileToS3(path, attribute_nft, file_name);
      });
      return RestAPI.success(res, {
        total: tickets.length,
        tickets: tickets,
      });
    } else {
      return RestAPI.badRequest(res, Constants.MSG.EVENT_IS_NOT_EXISTED_ERR);
    }
  }

  async createJsonFileToS3(path, text, filename) {
    // Save log to file.
    const directory = 'public/' + path + '/' + filename;
    await this.storagesService.upload(Bucket.MEDIA, directory, text, filename);

    return `${process.env.MEDIA_URL}/${path}/${filename}`;
  }

  async generateCNFTByTicket(req, payload, res) {
    const event_id = req.body.event_id;
    const event_stage_id = req.body.event_stage_id;
    const event_date_id = req.body.event_date_id;
    const event_zone_detail_id = req.body.event_zone_detail_id;
    const event_seat_price_id = req.body.event_seat_price_id;
    const pos = req.body.pos;

    const tickets = await this.getCnftTemps(
      {
        deleted: false,
        event_id: event_id,
        event_stage_id: event_stage_id,
        event_date_id: event_date_id,
        event_zone_detail_id: event_zone_detail_id,
        event_seat_id: event_seat_price_id,
        pos: pos,
        transaction: {
          [Op.is]: null,
        },
      },
      3,
    ); // limit 10
    // return RestAPI.success(res, tickets);
    const event_detail = await this.eventService.getEventDetail(event_id);
    const event_stage_date_detail =
      await this.eventStageDateService.getEventStageDateDetail(event_date_id);
    const event_zone_detail =
      await this.eventZoneDetailService.getEventZoneDetail(
        event_zone_detail_id,
      );
    if (event_detail) {
      tickets.forEach(async (element) => {
        // const prefixCode = Common.firstCodeGIFT() + event_detail.code;
        const prefixCode = event_detail.code;
        const code_order = prefixCode + Common.getRandomCode(10); // 13 ki tu

        // build cNFT
        const cnft_transaction = await ActivityNft.buildCNFTTemp(
          req,
          element,
          code_order,
          event_zone_detail,
          event_detail,
          event_stage_date_detail,
        );
        // var mint_address = await ActivityNft.burnCNFT();
        console.log(cnft_transaction);

        await this.cNftModel.update(
          {
            transaction: cnft_transaction.toString(),
            code_order: code_order,
          },
          {
            where: { id: element.id },
          },
        );
      });
      return RestAPI.success(res, {
        total: tickets.length,
      });
    } else {
      return RestAPI.badRequest(res, Constants.MSG.EVENT_IS_NOT_EXISTED_ERR);
    }
  }

  async burnCNFT(payload, res) {
    const assetId = payload.assetId;
    // var mint_address = await ActivityNft.getLeafID();
    const mint_address = await ActivityNft.burnCNFT(assetId);

    // update cart temp detail
    await this.cNftModel.update(
      {
        mint_address: null,
        transaction: null,
        code_order: null,
      },
      {
        where: { mint_address: assetId },
      },
    );
    return RestAPI.success(res, 'OK');
  }

  async updateCNFTAddress(payload, res) {
    const event_id = payload.event_id;
    const event_stage_id = payload.event_stage_id;
    const event_date_id = payload.event_date_id;
    const event_zone_detail_id = payload.event_zone_detail_id;
    const limit = payload.limit || 10;
    const offset = payload.offset || 0;
    console.log('>>>>> updateCNFTAddress > payload', payload);

    const tickets = await this.getCnftTemps(
      {
        deleted: false,
        // Trường hợp không có transaction
        // transaction: {
        //   [Op.not]: null,
        // },
        // mint_address: {
        //   [Op.is]: null,
        // },
        [Op.or]: [
          {
            mint_address: {
              [Op.is]: null,
            },
          },
          { mint_address: '' },
        ],
        event_id: event_id,
        event_stage_id: event_stage_id,
        event_date_id: event_date_id,
        event_zone_detail_id: event_zone_detail_id,
      },
      limit,
      offset,
    ); // limit 10

    const listCNFTs = await ActivityNft.getAllCnfts();
    console.log('listCNFTs length :>> ', listCNFTs.length, tickets);
    // console.log('listCNFTs :>> ', listCNFTs);
    for (let iItem = 0; iItem < tickets.length; iItem++) {
      const element = tickets[iItem];
      console.log('Element ticket item: >> ', element);

      let mint_address: null | string = null;
      try {
        if (element.transaction || !_.isEmpty(element.transaction)) {
          mint_address = await ActivityNft.getLeafID(element.transaction);
        } else {
          const element_json_url = `${process.env.MEDIA_URL}/tickets/${element.event_id}/${element.code_order}.json`;
          console.log('element_json_url >> path >> ', element_json_url);
          const cnft_item = listCNFTs.find(
            (v) => v.content.json_uri === element_json_url,
          );
          if (!_.isEmpty(cnft_item)) {
            mint_address = cnft_item.id;
          }
        }

        // Sử dụng json_uri để tìm mint_address
        if (_.isEmpty(mint_address)) {
          const element_json_url = `${process.env.MEDIA_URL}/tickets/${element.event_id}/${element.code_order}.json`;
          const cnft_item = listCNFTs.find(
            (v) => v.content.json_uri === element_json_url,
          );
          if (!_.isEmpty(cnft_item)) {
            mint_address = cnft_item.id;
          }
        }

        console.log(
          '======> element.code_order > mint_address',
          element.code_order,
          ' >> ',
          mint_address,
        );

        // Sử dụng mint_address để tìm transaction
        let cnft_transaction: Uint8Array | null = null;
        if (_.isEmpty(element.transaction)) {
          cnft_transaction =
            await ActivityNft.getCNFTTransactionByAssetId(mint_address);
        }

        // update cnft
        await this.cNftModel.update(
          {
            mint_address,
            transaction:
              element.transaction || cnft_transaction?.toString() || null,
          },
          {
            where: { id: element.id },
          },
        );
        console.log('>>>>>>>>>>>> update success :>> ', element.code_order);
      } catch (error) {
        console.log('error :>> ', error);
      }
    }
    return RestAPI.success(res, {
      total: tickets.length,
    });
  }

  async getCnftTempDetail(where_array) {
    return await this.cNftModel.findOne({
      where: where_array,
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  generateRandomTransaction(): string {
    const numberOfElements = 64;
    const maxNumber = 255;
    const randomNumbers = Array.from({ length: numberOfElements }, () => {
      // Generate a random number between 0 and 255
      return Math.floor(Math.random() * (maxNumber + 1));
    });
    return randomNumbers.join(',');
  }

  // max-length = 44
  generateRandomMintAddress(length: number = 34): string {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let result = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charactersLength);
      result += characters.charAt(randomIndex);
    }
    const currentUnixTimestamp = Math.floor(Date.now() / 1000);
    result += currentUnixTimestamp;
    return result;
  }

  async generateTicketQrByZone(payload, res) {
    const event_id = payload.event_id;
    const event_stage_id = payload.event_stage_id;
    const event_date_id = payload.event_date_id;
    const event_zone_detail_id = payload.event_zone_detail_id;

    const event_detail = await this.eventService.getEventDetail(event_id);
    const event_stage_date_detail =
      await this.eventStageDateService.getEventStageDateDetail(event_date_id);

    if (
      event_stage_date_detail &&
      event_stage_date_detail.ticket_type !== TICKET_TYPE.QR
    ) {
      return RestAPI.badRequest(
        res,
        Constants.MSG.EVENT_STAGE_DATE_TICKET_TYPE_IS_NOT_ACCEPTED_ERR,
      );
    }
    const event_zone_detail =
      await this.eventZoneDetailService.getEventZoneDetail(
        event_zone_detail_id,
      );
    if (event_detail && event_zone_detail) {
      if (
        event_zone_detail.event_id == event_id &&
        event_zone_detail.event_stage_id == event_stage_id &&
        event_zone_detail.event_date_id == event_date_id
      ) {
        const event_seat_price =
          await this.eventSeatPriceService.getListEventSeatPrice(
            event_zone_detail_id,
          );
        if (event_seat_price) {
          const cnft_inserts: any = [];
          for (
            let iSeatPrice = 0;
            iSeatPrice < event_seat_price.length;
            iSeatPrice++
          ) {
            const item = event_seat_price[iSeatPrice];
            if (item.total_seat_row > 0 && !_.isEmpty(item.seat_position)) {
              const seatPosition = Object.values(item.seat_position);
              const positionTableName = Object.values(
                item.seat_position_table_name,
              );

              for (let i = 0; i < seatPosition.length; i++) {
                const pos = seatPosition[i];

                // Bắt đầu tìm table cirlce cho seat number
                // trường hợp là table circle thì lấy name table như là seat row (row_code)
                if (!_.isEmpty(positionTableName)) {
                  const posRowTable = positionTableName[i];
                  _.assign(item, { row_code: posRowTable });
                }
                // Kết thúc

                const seat_name = pos < 10 ? '0' + pos : pos;
                //   const prefixCode = Common.firstCodeGIFT() + event_detail.code;
                const prefixCode = event_detail.code;
                const code_order = prefixCode + Common.getRandomCode(10); // 13 ki tu

                let convertFullSeatName = seat_name;
                if (
                  item.row_code &&
                  !_.isUndefined(item.row_code) &&
                  !_.isEmpty(item.row_code)
                ) {
                  convertFullSeatName = `${item.row_code}-${seat_name}`;
                }

                // Bắt đầu insert từng vé vào nft temp table
                cnft_inserts.push({
                  event_id: event_zone_detail.event_id,
                  event_stage_id: event_zone_detail.event_stage_id,
                  event_date_id: event_zone_detail.event_date_id,
                  event_zone_detail_id: event_zone_detail_id,
                  custom_ticket_id: 0,
                  event_seat_id: item.id,
                  seat_name: convertFullSeatName,
                  pos: pos,
                  row_code: item.row_code,
                  code_order,
                  transaction: this.generateRandomTransaction(),
                  mint_address: this.generateRandomMintAddress(),
                });
                // Kết thúc insert
              }
            }
          }

          // Tìm xem đã có vé tồn tại trong nft temp table chưa
          return await this.cNftModel
            .findAll({
              where: {
                event_id: event_zone_detail.event_id,
                event_stage_id: event_zone_detail.event_stage_id,
                event_date_id: event_zone_detail.event_date_id,
                event_zone_detail_id: event_zone_detail_id,
              },
              raw: true,
              nest: true,
            })
            .then(async (resFindAll) => {
              const cnft_unique: any = [];
              for (
                let i_cnft_insert = 0;
                i_cnft_insert < cnft_inserts.length;
                i_cnft_insert++
              ) {
                const element = cnft_inserts[i_cnft_insert];
                const findExistNft = _.find(resFindAll, {
                  event_id: element.event_id,
                  event_stage_id: element.event_stage_id,
                  event_date_id: element.event_date_id,
                  event_zone_detail_id: element.event_zone_detail_id,
                  event_seat_id: element.event_seat_id,
                  seat_name: element.seat_name,
                });
                if (!findExistNft) {
                  cnft_unique.push(element);
                }
              }

              let dataInserted: any = '';
              if (cnft_unique.length > 0) {
                try {
                  dataInserted = await this.cNftModel.bulkCreate(cnft_unique, {
                    ignoreDuplicates: true,
                  });
                  return RestAPI.success(res, {
                    dataInserted: dataInserted,
                    total: cnft_unique.length,
                  });
                } catch (error) {
                  return RestAPI.success(res, {
                    dataInserted: [],
                    total: cnft_unique.length,
                  });
                }
              } else {
                return RestAPI.success(res, {
                  dataInserted: [],
                  total: cnft_unique.length,
                });
              }
            });
          // Kết thúc tìm kiếm
        } else {
          return RestAPI.badRequest(
            res,
            Constants.MSG.EVENT_SEAT_PRICE_IS_NOT_EXISTED_ERR,
          );
        }
      } else {
        return RestAPI.badRequest(res, Constants.MSG.EVENT_IS_NOT_EXISTED_ERR);
      }
    } else {
      return RestAPI.badRequest(
        res,
        Constants.MSG.EVENT_ZONE_DETAIL_IS_NOT_EXISTED_ERR,
      );
    }
  }

  async generateTicketQrByEventID(payload, res) {
    const event_id = payload.event_id;
    const event_detail = await this.eventService.getEventDetail(event_id);

    return await this.eventZoneDetailService
      .getListEventZoneDetailByEventID(event_id)
      .then(async (listZoneDetail) => {
        if (listZoneDetail.length === 0) {
          return { result: false, data: 'ZONE_IS_EMPTY' };
        }

        for (let i = 0; i < listZoneDetail.length; i++) {
          const event_zone_detail = listZoneDetail[i];
          if (event_detail && event_zone_detail) {
            const event_seat_price =
              await this.eventSeatPriceService.getListEventSeatPrice(
                event_zone_detail.id,
              );
            if (event_seat_price) {
              const cnft_inserts: any = [];
              for (
                let iSeatPrice = 0;
                iSeatPrice < event_seat_price.length;
                iSeatPrice++
              ) {
                const item = event_seat_price[iSeatPrice];
                if (item.total_seat_row > 0 && !_.isEmpty(item.seat_position)) {
                  const seatPosition = Object.values(item.seat_position);

                  for (let i = 0; i < seatPosition.length; i++) {
                    const pos = seatPosition[i];

                    const seat_name = pos < 10 ? '0' + pos : pos;
                    //   const prefixCode = Common.firstCodeGIFT() + event_detail.code;
                    const prefixCode = event_detail.code;
                    const code_order = prefixCode + Common.getRandomCode(10); // 13 ki tu

                    let convertFullSeatName = seat_name;
                    if (
                      item.row_code &&
                      !_.isUndefined(item.row_code) &&
                      !_.isEmpty(item.row_code)
                    ) {
                      convertFullSeatName = `${item.row_code}-${seat_name}`;
                    }

                    // Bắt đầu insert từng vé vào nft temp table
                    cnft_inserts.push({
                      event_id: event_zone_detail.event_id,
                      event_stage_id: event_zone_detail.event_stage_id,
                      event_date_id: event_zone_detail.event_date_id,
                      event_zone_detail_id: event_zone_detail.id,
                      custom_ticket_id: 0,
                      event_seat_id: item.id,
                      seat_name: convertFullSeatName,
                      pos: pos,
                      row_code: item.row_code,
                      code_order,
                      transaction: this.generateRandomTransaction(),
                      mint_address: this.generateRandomMintAddress(),
                    });
                    // Kết thúc insert
                  }
                }
              }

              // Tìm xem đã có vé tồn tại trong nft temp table chưa
              return await this.cNftModel
                .findAll({
                  where: {
                    event_id: event_zone_detail.event_id,
                    event_stage_id: event_zone_detail.event_stage_id,
                    event_date_id: event_zone_detail.event_date_id,
                    event_zone_detail_id: event_zone_detail.id,
                  },
                  raw: true,
                  nest: true,
                })
                .then(async (resFindAll) => {
                  const cnft_unique: any = [];
                  for (
                    let i_cnft_insert = 0;
                    i_cnft_insert < cnft_inserts.length;
                    i_cnft_insert++
                  ) {
                    const element = cnft_inserts[i_cnft_insert];
                    const findExistNft = _.find(resFindAll, {
                      event_id: element.event_id,
                      event_stage_id: element.event_stage_id,
                      event_date_id: element.event_date_id,
                      event_zone_detail_id: element.event_zone_detail_id,
                      event_seat_id: element.event_seat_id,
                      seat_name: element.seat_name,
                    });
                    if (!findExistNft) {
                      cnft_unique.push(element);
                    }
                  }

                  let dataInserted: any = '';
                  if (cnft_unique.length > 0) {
                    try {
                      dataInserted = await this.cNftModel.bulkCreate(
                        cnft_unique,
                        {
                          ignoreDuplicates: true,
                        },
                      );
                      return {
                        result: true,
                        data: {
                          dataInserted: dataInserted,
                          total: cnft_unique.length,
                        },
                      };
                    } catch (error) {
                      return {
                        result: true,
                        data: {
                          dataInserted: [],
                          total: cnft_unique.length,
                        },
                      };
                    }
                  } else {
                    return {
                      result: true,
                      data: {
                        dataInserted: [],
                        total: cnft_unique.length,
                      },
                    };
                  }
                });
              // Kết thúc tìm kiếm
            } else {
              return {
                result: false,
                data: Constants.MSG.EVENT_SEAT_PRICE_IS_NOT_EXISTED_ERR,
              };
            }
          } else {
            return {
              result: false,
              data: Constants.MSG.EVENT_IS_NOT_EXISTED_ERR,
            };
          }
        }
      })
      .catch((error) => {
        return { result: false, data: error };
      });
  }

  async checkAddress(mint_address) {
    return await ActivityNft.getCNFTInfoByAssetId(mint_address);
  }
}
