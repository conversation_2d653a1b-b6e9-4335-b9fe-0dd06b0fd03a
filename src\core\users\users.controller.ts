import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Patch,
  UseGuards,
  Req,
  Res,
  Query,
} from '@nestjs/common';
import _ from 'lodash';
import { InternalAPIGuard } from 'guards/internalAPI.guard';
import RestAPI from 'common/rest_api';
import { UsersService } from './users.services';

@Controller('')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('/users/get-all-user-role')
  @UseGuards(InternalAPIGuard)
  async getAllUserRole(
    @Query()
    params: { role: string; limit: number },
    @Res() res: Response,
  ): Promise<any> {
    const listData = await this.usersService.getAllUserRole(params);
    return RestAPI.success(res, listData);
  }
}
