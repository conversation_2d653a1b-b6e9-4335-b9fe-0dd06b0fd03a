{"containerDefinitions": [{"name": "worker", "image": "056547273288.dkr.ecr.ap-southeast-1.amazonaws.com/staging-worker:latest", "cpu": 128, "memory": 256, "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/staging/", "awslogs-create-group": "true", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "worker"}}}], "family": "staging-worker", "executionRoleArn": "arn:aws:iam::056547273288:role/vtix-staging-ecs-task-execute", "networkMode": "bridge"}