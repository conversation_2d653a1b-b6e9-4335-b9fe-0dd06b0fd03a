import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Event } from '@events/models/event.model';
import { EventZoneDetailsService } from './event_zone_details.services';
import { EventZoneDetail } from './models/event_zone_detail.model';

@Module({
  imports: [SequelizeModule.forFeature([EventZoneDetail])],
  providers: [EventZoneDetailsService],
  exports: [EventZoneDetailsService],
})
export class EventZoneDetailsModule {}
