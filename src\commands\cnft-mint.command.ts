import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

@Command({
  name: 'process-mint',
  description: 'Process mint address',
})
@Injectable()
export class CnftMintCommand extends CommandRunner {
  private readonly logger = new Logger(CnftMintCommand.name);
  private batchSize = 50; // 10; // Default batch size
  private timeDelayCallMint = 3 * 1000; // 5 minutes
  private timeDelayNextZone = 0.5 * 60 * 1000; // 2 minutes
  private isProcessingComplete = false;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // ** LƯU Ý: chỉ chạy sau khi hoàn thành tạo transactions được ít nhất 1 tiếng.
  // Thứ tự chạy:
  // B1: Sẽ lấy 10 transaction vừa tạo trong bảng cnft_temp để mint-address
  // B2: Thời gian deplay 5 phút
  // B3: Sau khi mint xong, sẽ update address này vào lại bảng cnft_temp
  // VD: npx ts-node src/cli.ts process-mint --event_id="a42f0c9c-7dbd-4f58-ad14-adb78c98168f" --event_stage_id=8 --event_date_id=18
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const limit = options?.batchSize || this.batchSize;
    const event_id = options?.event_id;
    const event_stage_id = options?.event_stage_id;
    const event_date_id = options?.event_date_id;
    const event_zone_detail_id = options?.event_zone_detail_id;

    console.info({
      limit,
      event_id,
      event_stage_id,
      event_date_id,
      event_zone_detail_id,
    });

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };
    // const payload = {
    //   event_id,
    //   event_stage_id,
    //   event_date_id,
    //   event_zone_detail_id,
    // };

    const getEventZoneIds = await firstValueFrom(
      this.httpService.get(
        `${app_domain}/_api/events/get-list-event-date-by-zone?event_date_id=${event_date_id}`,
        { headers },
      ),
    );
    const listEventZoneIds = getEventZoneIds.data.data || [];
    if (listEventZoneIds.length === 0) {
      console.info('No event zone found');
      return;
    }

    for (const eventZoneId of listEventZoneIds) {
      const payload = {
        event_id,
        event_stage_id,
        event_date_id,
        event_zone_detail_id: eventZoneId,
      };

      const getTotalMintAddressRemain = await firstValueFrom(
        this.httpService.post(
          `${app_domain}/_api/cnft/total-mint-address-remain`,
          payload,
          { headers },
        ),
      );
      const totalMintAddressRemain =
        getTotalMintAddressRemain.data.data.total ?? 0;
      if (totalMintAddressRemain === 0) {
        console.info('No mint address remain');
        continue;
      }
      const callNumber = Math.ceil(totalMintAddressRemain / limit);
      console.info(
        `Starting mint address with Zone: ${eventZoneId} - total: ${totalMintAddressRemain} - call: ${callNumber} times`,
      );
      for (let i = 1; i <= callNumber; i++) {
        console.info(
          `----Call ${i} time with ${JSON.stringify({ ...payload, limit: limit })}`,
        );
        const transactions = await firstValueFrom(
          this.httpService.post(
            `${app_domain}/_api/cnft/update-cnft-address`,
            { ...payload, limit: limit, offset: i - 1 },
            { headers },
          ),
        );
        console.info(
          '--------Generate success - total: ',
          transactions.data.data.total,
        );
        await this.delayTime(this.timeDelayCallMint);
      }
      await this.delayTime(this.timeDelayNextZone);
    }
    console.info('<<<<<<<<<<<All mint address have been updated.>>>>>>>>>>>>>');
  }

  @Option({
    flags: '-b, --batchSize <batchSize>',
    description: 'Batch size for processing',
  })
  parseBatchSize(val: string): number {
    return parseInt(val, this.batchSize);
  }

  @Option({
    flags: '-event_id, --event_id <event_id>',
  })
  parseEventId(val: string): string {
    return val;
  }

  @Option({
    flags: '-event_stage_id, --event_stage_id <event_stage_id>',
  })
  parseEventStageId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_date_id, --event_date_id <event_date_id>',
  })
  parseEventDateId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_zone_detail_id, --event_zone_detail_id <event_date_id>',
  })
  parseEventZoneDetailId(val: string): number {
    return parseInt(val);
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }
}
