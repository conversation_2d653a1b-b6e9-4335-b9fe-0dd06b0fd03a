import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { ParticipantInfo } from './models/participants_info.model';
import _ from 'lodash';

@Injectable()
export class ParticipantInfoService {
  constructor(
    @InjectModel(ParticipantInfo)
    private readonly participantInfoModel: typeof ParticipantInfo,
    private sequelize: Sequelize,
  ) { }

  async getListParticipantInfo(conditions: any) {
    return await this.participantInfoModel.findAll({
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async getParticipantInfoDetailByTempSeatId(temp_seats_id) {
    return await this.participantInfoModel.findOne({
      where: {
        temp_seats_id,
      },
      raw: true, // <----------- <PERSON> is here
    });
  }

  async getParticipantInfoDetailByTempQuantityId(temp_quantities_id) {
    return await this.participantInfoModel.findOne({
      where: {
        temp_quantities_id,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async getParticipantInfoDetail(conditions: Partial<ParticipantInfo>) {
    return await this.participantInfoModel.findOne({
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async updateStatusParticipantInfo(transaction_id, status) {
    return await this.participantInfoModel.update(
      { status },
      { where: { transaction_id } },
    );
  }
}
