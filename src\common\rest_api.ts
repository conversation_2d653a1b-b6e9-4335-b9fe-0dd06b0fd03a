import { HttpCode, HttpStatus } from '@nestjs/common';
import Common from '../common/common';
import Constants from '../common/constants';
import * as process from 'process';

type ErrorType = null | { message: string };

class RestAPI {
  static notFound(res, message, code = '') {
    return res
      .status(HttpStatus.NOT_FOUND)
      .json({ status: 404, message: message, code });
  }

  static forbidden(res, message) {
    return res
      .status(HttpStatus.FORBIDDEN)
      .json({ status: 404, message: message });
  }

  static unauthorizedVietqr(res) {
    const dataRes = {
      error: true,
      errorReason: '401',
      toastMessage: 'Unauthorized',
    };
    return res.status(HttpStatus.UNAUTHORIZED).json(dataRes);
  }

  static unauthorized(res, message, error = null) {
    const dataRes: { status: number; message: string; error?: any } = {
      status: 401,
      message: message,
    };
    if (!Common.isEmpty(error)) {
      dataRes.error = error;
    }
    return res.status(HttpStatus.UNAUTHORIZED).json(dataRes);
  }

  static badRequest(res, message, error: ErrorType = null, data: any = null) {
    const dataRes: { status: number; message: string; error?: any } = {
      status: 400,
      message: message,
    };
    if (data != null) {
      dataRes['data'] = data;
    }
    if (error !== null) {
      Common.dLog(error.message);
      dataRes['error'] = error.message;
    }
    return res.status(HttpStatus.BAD_REQUEST).json(dataRes);
  }

  static badRequest_raw(res, data, keyAdditional = null) {
    if (Common.isEmpty(data)) {
      return { status: 200, message: 'OK' };
    } else {
      data = removeEmpty(data);
      let response = data;
      if (keyAdditional != null) {
        response = Object.assign({}, response, keyAdditional);
      }
      return res.status(HttpStatus.BAD_REQUEST).json(response);
    }
  }

  static serverError(res, message = '', error: ErrorType = null) {
    if (error != null) {
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ status: 500, message: message, error: error.message });
    } else {
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ status: 500, message: message });
    }
  }

  static success(res, data: any = null, keyAdditional = null) {
    if (Common.isEmpty(data)) {
      return res.status(HttpStatus.OK).json({ status: 200, message: 'OK' });
    } else {
      // data = removeEmpty(data);
      let response = { status: 200, message: 'OK', data: data };
      if (keyAdditional != null) {
        response = Object.assign({}, response, keyAdditional);
      }
      return res.status(HttpStatus.OK).json(response);
    }
  }

  static success_raw(res, data, keyAdditional = null) {
    if (Common.isEmpty(data)) {
      return res.status(200).json({ status: 200, message: 'OK' });
    } else {
      data = removeEmpty(data);
      let response = data;
      if (keyAdditional != null) {
        response = Object.assign({}, response, keyAdditional);
      }
      return res.status(200).json(response);
    }
  }

  static success_app(res, data: any = null, keyAdditional = null) {
    const response = { status: 200, message: 'OK', data: data };
    return res.status(HttpStatus.OK).json(response);
  }

  static handleCatch(res, error) {
    const env = process.env.NODE_ENV;
    if (env == 'production') {
      this.serverError(res, Constants.MSG.SERVER_ERR, error);
    } else {
      this.serverError(res, Constants.MSG.SERVER_ERR, error);
      throw error;
    }
  }

  static successListAdmin(res, data, error: ErrorType = null) {
    if (!Common.isEmpty(error) && error !== null) {
      data['message'] = error.message;
    }
    if (Common.isEmpty(data.data)) {
      return res
        .status(HttpStatus.OK)
        .json(Object.assign({}, { status: 200 }, data));
    } else {
      data['data'] = removeEmpty(data.data);
      // merge two dictionaries
      return res
        .status(HttpStatus.OK)
        .json(Object.assign({}, { status: 200 }, data));
    }
  }
}

function removeEmpty<T>(obj: T): T | null {
  // Clone the object
  obj = JSON.parse(JSON.stringify(obj));

  if (Common.isEmpty(obj)) {
    return null;
  } else if (Array.isArray(obj)) {
    const newArray: any[] = [];
    obj.forEach((item) => {
      const cleanedItem = removeEmpty(item);
      if (cleanedItem !== null) {
        newArray.push(cleanedItem);
      }
    });
    return newArray as T;
  } else if (typeof obj === 'object' && obj !== null) {
    const newObj: { [key: string]: any } = {};
    Object.keys(obj).forEach((key) => {
      if (Array.isArray(obj[key])) {
        const newArray: any[] = [];
        obj[key].forEach((item) => {
          const cleanedItem = removeEmpty(item);
          if (cleanedItem !== null) {
            newArray.push(cleanedItem);
          }
        });
        newObj[key] = newArray;
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        const cleanedValue = removeEmpty(obj[key]); // Recurse
        if (cleanedValue !== null) {
          newObj[key] = cleanedValue;
        }
      } else if (obj[key] != null) {
        newObj[key] = obj[key];
      }
    });

    return newObj as T;
  } else {
    return obj;
  }
}

export default RestAPI;
