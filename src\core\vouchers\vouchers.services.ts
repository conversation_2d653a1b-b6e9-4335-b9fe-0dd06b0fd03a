import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Voucher } from './models/voucher.model';
import { CreationAttributes } from 'sequelize';
import { VoucherProvider } from 'core/voucher_providers/models/voucher_provider.model';
import _ from 'lodash';
import { StoragesService } from 'storages/storages.service';
import { Bucket } from 'utils/buckets';

@Injectable()
export class VoucherService {
  constructor(
    @InjectModel(Voucher)
    private readonly voucherModel: typeof Voucher,
    @InjectModel(VoucherProvider)
    private readonly voucherProviderModel: typeof VoucherProvider,

    private storagesService: StoragesService,
  ) { }

  async getListVoucher(conditions: any) {
    const { page = 1, limit = 10, ...rest } = conditions;
    const offset = (page - 1) * limit;
    const [result, total] = await Promise.all([
      this.voucherModel.findAll({
        where: {
          ...rest,
        },
        offset,
        limit,
        raw: true,
      }),
      this.voucherModel.count({
        where: {
          ...rest,
        },
      }),
    ]);

    return {
      list: result,
      total,
      limit: parseInt(limit),
      page: parseInt(page),
    };
  }

  async getVoucherDetail(conditions: any) {
    return await this.voucherModel.findOne({
      where: {
        ...conditions,
      },
      raw: true, // <----------- Magic is here
    });
  }

  async uploadVoucherImageToS3(path: string, text: Buffer, filename: string) {
    // Save log to file.
    const directory = 'public/' + path + '/' + filename;
    await this.storagesService.upload(Bucket.MEDIA, directory, text, filename);

    return `/${path}/${filename}`;
  }

  async create(payload: any) {
    let { voucher_provider_id, photo } = payload;
    const voucherProvider = await this.voucherProviderModel.findOne({
      where: {
        id: voucher_provider_id,
      },
    });
    if (!voucherProvider) {
      throw new Error('Voucher provider not found');
    }
    const totalAmount = voucherProvider.total_amount;
    const totalVoucher = await this.voucherModel.count({
      where: {
        voucher_provider_id,
      },
    });
    if (totalAmount <= totalVoucher) {
      throw new Error('Voucher exceeded');
    }
    // if (photo && !_.isEmpty(photo)) {
    //   photo = await this.uploadVoucherImageToS3('vouchers', Buffer.from(photo), photo);
    // }
    return await this.voucherModel.create(
      {
        ...payload,
        photo,
      } as CreationAttributes<Voucher>,
    );
  }

  async bulkCreate(dataInsert: any[]) {
    return await this.voucherModel.bulkCreate(dataInsert);
  }

  async removeVoucher(conditions: any): Promise<any> {
    try {
      await this.voucherModel.destroy({
        where: {
          ...conditions,
        },
      });
    } catch (error) {
      console.log('error :>> ', error.message);
    }
  }

  async updateVoucher(id: string, payload: any) {
    return await this.voucherModel.update(payload, { where: { id } });
  }

  async updateStatusVoucher(id: string, status: boolean) {
    return await this.voucherModel.update({ status }, { where: { id } });
  }
}
