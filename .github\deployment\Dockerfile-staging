# === Builder Stage ===
FROM node:20-alpine AS builder

WORKDIR /usr/src/app
RUN apk add --no-cache \
    make \
    g++ \
    python3 \
    linux-headers \
    eudev-dev \
    libusb-dev

COPY package*.json ./
RUN npm install

# === Final Stage ===
FROM node:20-alpine

WORKDIR /usr/src/app

# Install Chromium and all required dependencies for headless mode

ENV NODE_OPTIONS="--max-old-space-size=8192"

# Copy application code and environment file
COPY .env.staging .env
COPY . .

COPY --from=builder /usr/src/app/node_modules ./node_modules

RUN npm run build

CMD ["npm", "run", "start:prod"]