import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import { SendMailTicketService } from './send_mail_ticket_task.service';
import { Event } from '@events/models/event.model';
import { User } from '@users/models/user.model';
import { EventStagesModule } from '@event_stages/event_stages.module';
import { EventStageDatesModule } from '@event_stage_dates/event_stage_dates.module';
import { EventZoneDetailsModule } from '@event_zone_details/event_zone_details.module';
import { CouponsModule } from '@coupons/coupons.module';
import { ParticipantInfoModule } from '@participants_info/participants_info.module';
import { TempSeat } from '@temp_seats/models/temp_seat.model';
import { TempQuantity } from '@temp_quantities/models/temp_quantity.model';
import { CNftTemp } from '@cnft_temps/models/cnft_temp.model';
import { TempSeatsModule } from '@temp_seats/temp_seats.module';
import { TempQuantitiesModule } from '@temp_quantities/temp_quantities.module';
import { CNftTempsModule } from '@cnft_temps/cnft_temps.module';
import { Transaction } from '@transactions/models/transaction.model';
import { UserHasVouchersModule } from '@user_has_vouchers/user_has_vouchers.module';
import { EventSeatPricesModule } from '@event_seat_prices/event_seat_prices.module';
import { TelegramService } from 'core/telegram/telegram.service';
@Module({
  imports: [
    SequelizeModule.forFeature([PaymentDetail, Event, User, TempSeat, TempQuantity, CNftTemp, Transaction]),
    EventStagesModule,
    EventStageDatesModule,
    EventZoneDetailsModule,
    CouponsModule,
    ParticipantInfoModule,
    TempSeatsModule,
    TempQuantitiesModule,
    CNftTempsModule,
    UserHasVouchersModule,
    EventSeatPricesModule,
  ],
  providers: [SendMailTicketService, TelegramService],
})
export class TasksModule { }
