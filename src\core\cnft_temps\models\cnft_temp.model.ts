import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  UpdatedAt,
  Unique,
  PrimaryKey,
} from 'sequelize-typescript';
import { Event } from '@events/models/event.model';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';

@Table({
  tableName: 'cnft_temp',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class CNftTemp extends Model<CNftTemp> {
  @PrimaryKey
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => Event) // Assuming you have an Event model
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID event cập nhật từ table `event` sau khi được tạo ra.',
  })
  event_id: string;

  @ForeignKey(() => EventStage) // Assuming you have an EventStage model
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Stage',
  })
  event_stage_id: number;

  @ForeignKey(() => EventStageDate) // Assuming you have an EventStageDate model
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event DATE',
  })
  event_date_id: number;

  @ForeignKey(() => EventZoneDetail) // Assuming you have an EventZoneDetail model
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event Zone detail',
  })
  event_zone_detail_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Custom Ticket ID',
  })
  custom_ticket_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Event seat id',
  })
  event_seat_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'So ghe',
  })
  seat_name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Vi tri',
  })
  pos: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'hang',
  })
  row_code: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'NFT ADDRESS',
  })
  mint_address: string | null;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Cart Detail Temp ID',
  })
  cart_detail_temp_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  transaction: string | null;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '0 - chua gui mail / 1 - da gui mail',
  })
  sent_mail: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Order detail',
  })
  code_order: string | null;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  // @UpdatedAt
  // @Column({
  //   type: DataType.DATE,
  //   allowNull: false,
  //   defaultValue: DataType.NOW,
  // })
  // updated_at: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;
}
