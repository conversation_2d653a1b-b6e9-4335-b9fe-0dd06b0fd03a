import { VoucherProvider } from '@voucher_providers/models/voucher_provider.model';
import {
  Table,
  Column,
  Model,
  PrimaryKey,
  DataType,
  BelongsTo,
  Default,
} from 'sequelize-typescript';

@Table({
  tableName: 'vouchers',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class Voucher extends Model<Voucher> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: 'ID voucher provider',
    references: {
      model: 'voucher_providers',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  voucher_provider_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Tên voucher',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '<PERSON>ô tả voucher',
  })
  description: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Mã voucher',
    unique: true,
  })
  code: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Ảnh voucher',
  })
  photo: string;

  @Column({
    type: DataType.ENUM('percentage', 'fixed'),
    allowNull: true,
    defaultValue: 'percentage',
  })
  discount_type: 'percentage' | 'fixed';

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Giá trị giảm giá (tỷ lệ phần trăm hoặc số tiền cố định)',
  })
  discount_value: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Thời gian bắt đầu (Unix timestamp)',
  })
  from_date: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Thời gian kết thúc (Unix timestamp)',
  })
  to_date: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  status: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  created_at: Date;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: {},
  })
  attributes: JSON;

  @BelongsTo(() => VoucherProvider, 'voucher_provider_id')
  voucher_provider: VoucherProvider;
}
