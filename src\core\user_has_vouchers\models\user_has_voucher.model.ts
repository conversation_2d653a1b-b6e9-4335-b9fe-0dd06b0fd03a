import { User } from '@users/models/user.model';
import { Voucher } from '@vouchers/models/voucher.model';
import {
  Table,
  Column,
  Model,
  PrimaryKey,
  DataType,
  Default,
  BelongsTo,
} from 'sequelize-typescript';

@Table({
  tableName: 'user_has_vouchers',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class UserHasVoucher extends Model<UserHasVoucher> {
  @PrimaryKey
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
  })
  id: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: 'ID user',
    references: {
      model: 'users',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  user_id: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: 'ID voucher',
    references: {
      model: 'vouchers',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  voucher_id: string;

  @Column({
    type: DataType.ENUM('active', 'used', 'expired'),
    allowNull: false,
    defaultValue: 'active',
    comment: 'Trạng thái voucher của người dùng',
  })
  status: 'active' | 'used' | 'expired';

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  status_email: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: 'Thời điểm sử dụng voucher',
  })
  used_at: Date;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  created_at: Date;

  @Default(DataType.NOW)
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;

  @BelongsTo(() => User, { foreignKey: 'user_id', as: 'user' })
  user: User;

  @BelongsTo(() => Voucher, { foreignKey: 'voucher_id', as: 'voucher' })
  voucher: Voucher;
}
