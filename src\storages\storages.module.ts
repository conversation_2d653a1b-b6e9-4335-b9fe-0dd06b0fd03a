import { Modu<PERSON> } from '@nestjs/common';
import { StoragesService } from './storages.service';
import { S3Provider } from './s3.provider';
import { CloudFrontService } from './cloudfront.service';
import { CloudFrontProvider } from './cloudfront.provider';

const storageServiceProvider = {
  provide: StoragesService,
  useClass: S3Provider,
};

const cloudFrontServiceProvider = {
  provide: CloudFrontService,
  useClass: CloudFrontProvider,
};

@Module({
  providers: [storageServiceProvider],
  exports: [StoragesService],
})
export class StoragesModule {}
