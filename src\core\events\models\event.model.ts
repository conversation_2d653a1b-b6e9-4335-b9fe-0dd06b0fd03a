import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { fn } from 'sequelize';
import { User } from '@users/models/user.model';
import { EventStage } from '@event_stages/models/event_stage.model';

interface Attributes {
  [key: string]: any;
}

@Table({
  tableName: 'event',
  schema: 'public',
  timestamps: true,
  createdAt: false,
  updatedAt: false,
})
export class Event extends Model<Event> {
  @Column({
    type: DataType.UUID,
    allowNull: false,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    unique: 'event_id_key',
  })
  id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  name_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  name_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  slug_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  slug_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  meta_image_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  meta_image_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  meta_title_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  meta_title_en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  meta_description_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  meta_description_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  poster_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  poster_en: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment:
      '1: Event | 2: Workshop | 3: LGBT Show | 4: Sport | 5: Family Playground',
  })
  event_group_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 3811,
  })
  state_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'mo ta gia - VI',
  })
  ticket_price_desc_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'mo ta gia - EN',
  })
  ticket_price_desc_en: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngay bat dau dien ra su kien',
  })
  showdate_from: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngay ket thuc dien ra su kien',
  })
  showdate_to: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngay ban ve',
  })
  public_sale: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Ngay kết thúc ban ve',
  })
  public_sale_to: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'code tao ma don hang',
  })
  code: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Địa điểm - VI',
  })
  venue_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Địa điểm - EN',
  })
  venue_en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Nội dung - VI',
  })
  content_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Nội dung - EN',
  })
  content_en: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  deleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: fn('now'),
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: fn('now'),
  })
  updated_at: Date;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: 'ID user cập nhật từ table `users` sau khi được tạo ra.',
  })
  uid: string;

  @Column({
    type: DataType.STRING,
    defaultValue: 'banking|credit|momo|vietqr',
    comment: 'Event Payment Gateways: Banking | Credit | Momo | Vietqr',
  })
  payment_gateways: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Event demo',
  })
  is_demo: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
    comment:
      'Event này có phải là event free hay không? true: free | false: no free',
  })
  is_free_ticket: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  is_hot: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  is_coming_soon: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 1,
  })
  event_type: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
    comment:
      'Cho phép hiển thị chi tiết event hay không? true: show | false: hide',
  })
  is_show_detail: boolean;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Hiển thị banner event khi xem chi tiết',
  })
  banner_desktop_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Hiển thị banner event khi xem chi tiết',
  })
  banner_desktop_en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Hiển thị banner event khi xem chi tiết',
  })
  banner_mobile_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Hiển thị banner event khi xem chi tiết',
  })
  banner_mobile_en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Hiển thị trong email khi order thành công',
  })
  email_poster_vi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Hiển thị trong email khi order thành công',
  })
  email_poster_en: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '0: Không hiển thị | 1: Mã khuyến mãi | 2: Các thông báo khác',
  })
  type_highlight_note: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Nội dung ngắn cần hiển thị ở highlight noted',
  })
  content_highlight_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Nội dung ngắn cần hiển thị ở highlight noted',
  })
  content_highlight_en: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'true: Cho phép hiển thị ở frontend | false: ẩn',
  })
  is_display: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hiển thị text tại vị trí show date khi kích hoạt unlimited',
  })
  desc_unlimited_vi: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Hiển thị text tại vị trí show date khi kích hoạt unlimited',
  })
  desc_unlimited_en: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: {
      expect_text: 'ticket',
    },
  })
  attributes: Attributes;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 100,
    comment:
      'Thứ tự order cho event, mặc định là 100, số càng nhỏ sẽ càng được ưu tiên',
  })
  order_by: number;

  // Associations
  @HasMany(() => EventStage, {
    foreignKey: 'event_id',
    as: 'event_stage',
    onDelete: 'CASCADE',
  })
  event_stage: EventStage[];

  @BelongsTo(() => User, {
    foreignKey: 'uid',
    as: 'user',
  })
  user: User;
}
