name: Deploy worker to production environment

on:
  push:
    branches: [main]

permissions:
  id-token: write 
  contents: read

env:
  ECR_REGISTRY: 056547273288.dkr.ecr.ap-southeast-1.amazonaws.com
  ECR_REPOSITORY: production-worker
  TASK_DEFINITION: .github/deployment/task-definition-production.json
  TELEGRAM_DEPLOY_NOTIFIER_BOT_TOKEN: **********************************************
  TELEGRAM_DEPLOY_NOTICE_RECIPIENT_ID: -4704542493

jobs:  
  build-deploy:
    runs-on: [self-hosted, linux, X64]
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::056547273288:role/vtix-staging-github
          aws-region: ap-southeast-1

      - name: Sanding the start Telegram notification
        run: |
          curl --get \
            --data-urlencode 'chat_id=${{env.TELEGRAM_DEPLOY_NOTICE_RECIPIENT_ID}}' \
            --data-urlencode "text=🚀Start! ${{github.event.head_commit.message}} (${{github.event.head_commit.id}})" \
            https://api.telegram.org/bot${{env.TELEGRAM_DEPLOY_NOTIFIER_BOT_TOKEN}}/sendMessage

      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ${{ env.ECR_REGISTRY }}

      - name: Build, tag, and push image to Amazon ECR
        run: | 
          docker buildx build . -f .github/deployment/Dockerfile-production -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ github.sha }} -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:latest --push

      - name: Fill in the new image ID in the Amazon ECS task definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.TASK_DEFINITION }}
          container-name: worker
          image: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ github.sha }}
          task-definition-family: ${{ env.ECR_REPOSITORY }}
          log-configuration-options: |
            awslogs-create-group=true
            awslogs-group=/aws/ecs/production/
            awslogs-region=ap-southeast-1
            awslogs-stream-prefix=${{ env.ECR_REPOSITORY }}


      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ env.TASK_DEFINITION }}
          service: worker
          cluster: vtix-production-ecs
          wait-for-service-stability: true
        timeout-minutes: 15
      
      - name: Sanding the result Telegram  notification
        run: |
          curl --get \
            --data-urlencode 'chat_id=${{env.TELEGRAM_DEPLOY_NOTICE_RECIPIENT_ID}}' \
            --data-urlencode 'text=✅SUCCESS! ${{github.event.head_commit.message}} (${{github.event.head_commit.id}})' \
            https://api.telegram.org/bot${{env.TELEGRAM_DEPLOY_NOTIFIER_BOT_TOKEN}}/sendMessage
  
  debug:
    name: Check failure
    needs: build-deploy
    runs-on: [self-hosted, linux, X64]
    if: ${{ failure() }}
    steps:
      - name: Sanding the failure Telegram notification
        run: |
          curl --get \
            --data-urlencode 'chat_id=${{env.TELEGRAM_DEPLOY_NOTICE_RECIPIENT_ID}}' \
            --data-urlencode 'text=🆘FAILED! ${{github.event.head_commit.message}} (${{github.event.head_commit.id}})' \
            https://api.telegram.org/bot${{env.TELEGRAM_DEPLOY_NOTIFIER_BOT_TOKEN}}/sendMessage