NODE_ENV=development
APP_PORT=4001
APP_NAME="VTix NFT"
APP_DOMAIN=http://localhost:4001
APP_CLIENT_DOMAIN=https://staging.vtix.vn
API_PREFIX=api

DATABASE_TYPE=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=vtix

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0

FILE_DRIVER=s3
ACCESS_KEY_ID=********************
SECRET_ACCESS_KEY=yML/qQQ9VCRYqIwjfhzmeOmJgLD3w5qhEjcgkhuC
AWS_S3_REGION=ap-southeast-1
AWS_S3_MEDIA_BUCKET=vtix-production-media

INTERNAL_X_KEY=32db9f510837eb8ff92eaee85ddf850c164672eb37c9f2dc9fcc654eab41b046

AUTH_JWT_SECRET=2161d43a04c40f59305944535e38a6a74d8a395b4c369c03105342aa3a963bd8a928b2043db77e340b523547bf16cb4aa483f0645fe0a290ed1f20aab76257aca6f7b874ea69329372ad75353314d7bcacd8c0be365023dab195bcac015d6009
AUTH_JWT_TOKEN_EXPIRES_IN=60m
AUTH_REFRESH_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmdWxsbmFtZSI6IlRy4bqnbiBNaW5oIFR1eeG6v24iLCJ1c2VySWQiOjE4NCwiYWNjb3VudF9pZCI6MTg2LCJhcHBfaWQiOiJ0dXllbjEyMyIsImRldmljZV9pZCI6InR1eWVuMTIzIiwidGltZV9taSI6MTYyNzI5MTY2NjAwMCwiaWF0IjoxNjI3MjkxNjY2LCJleHAiOjE2Mjk4ODM2NjZ9.vwUSrE4DpGbZw6goRLsaXfTderVdgVZ4ZZVii-B3nw4
AUTH_REFRESH_TOKEN_EXPIRES_IN=7d

MEDIA_URL=https://media.vtix.vn
