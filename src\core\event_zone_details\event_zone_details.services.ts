import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { EventZoneDetail } from './models/event_zone_detail.model';

@Injectable()
export class EventZoneDetailsService {
  constructor(
    @InjectModel(EventZoneDetail)
    private readonly eventZoneDetailModel: typeof EventZoneDetail,
    private sequelize: Sequelize,
  ) {}

  async getEventZoneDetail(event_zone_detail_id) {
    return await this.eventZoneDetailModel.findOne({
      where: {
        active: true,
        deleted: false,
        id: event_zone_detail_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- <PERSON> is here
    });
  }

  async getListEventZoneDetail(event_date_id) {
    return await this.eventZoneDetailModel.findAll({
      where: {
        active: true,
        deleted: false,
        event_date_id: event_date_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  async getListEventZoneDetailByEventID(event_id) {
    return await this.eventZoneDetailModel.findAll({
      where: {
        active: true,
        deleted: false,
        event_id: event_id,
      },
      order: [['id', 'ASC']],
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }
}
