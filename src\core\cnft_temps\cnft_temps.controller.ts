import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Patch,
  UseGuards,
  Req,
  Res,
} from '@nestjs/common';
import { CNftTempsService } from './cnft_temps.services';
import { InternalAPIGuard } from 'guards/internalAPI.guard';
import RestAPI from 'common/rest_api';

@Controller('cnft')
export class CNftController {
  constructor(private readonly cNftTempsService: CNftTempsService) {}

  @Post('/generate-ticket-by-zone')
  @UseGuards(InternalAPIGuard)
  generateTicketsByZone(@Body() payload, @Res() res: Response): Promise<any> {
    return this.cNftTempsService.generateTicketsByZone(payload, res);
  }

  @Post('/total-transaction-remain')
  @UseGuards(InternalAPIGuard)
  getTotalTransactionRemain(
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    const conditions = {
      event_id: payload.event_id,
      event_stage_id: payload.event_stage_id,
      event_date_id: payload.event_date_id,
      event_zone_detail_id: payload.event_zone_detail_id,
    };
    return this.cNftTempsService.getTotalTransactionRemain(conditions, res);
  }

  @Post('/total-cnft-temp')
  @UseGuards(InternalAPIGuard)
  getTotalCNFTTemp(
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    const conditions = {
      event_id: payload.event_id,
      event_stage_id: payload.event_stage_id,
      event_date_id: payload.event_date_id,
      event_zone_detail_id: payload.event_zone_detail_id,
    };
    return this.cNftTempsService.getTotalCNFTTemp(conditions, res);
  }

  @Post('/update-cnft-json-by-zone')
  @UseGuards(InternalAPIGuard)
  updateCNFTJsonByTicket(
    @Req() req: Request,
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    return this.cNftTempsService.updateCNFTJsonByTicket(req, payload, res);
  }

  @Post('/generate-cnft-by-zone')
  @UseGuards(InternalAPIGuard)
  generateCNFTByTickets(
    @Req() req: Request,
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    return this.cNftTempsService.generateCNFTByTickets(req, payload, res);
  }

  @Post('/generate-one-cnft-by-zone')
  @UseGuards(InternalAPIGuard)
  generateCNFTByTicket(
    @Req() req: Request,
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    return this.cNftTempsService.generateCNFTByTicket(req, payload, res);
  }

  @Post('/burn-cnft')
  @UseGuards(InternalAPIGuard)
  burnCNFT(
    @Req() req: Request,
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    return this.cNftTempsService.burnCNFT(payload, res);
  }

  @Post('/total-mint-address-remain')
  @UseGuards(InternalAPIGuard)
  getTotalMintAddressRemain(
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    const conditions = {
      event_id: payload.event_id,
      event_stage_id: payload.event_stage_id,
      event_date_id: payload.event_date_id,
      event_zone_detail_id: payload.event_zone_detail_id,
    };
    return this.cNftTempsService.getTotalMintAddressRemain(conditions, res);
  }

  @Post('/update-cnft-address')
  @UseGuards(InternalAPIGuard)
  updateCNFTAddress(
    @Req() req: Request,
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    return this.cNftTempsService.updateCNFTAddress(payload, res);
  }

  @Post('/generate-ticket-qr-by-zone')
  @UseGuards(InternalAPIGuard)
  generateTicketQrByZone(@Body() payload, @Res() res: Response): Promise<any> {
    console.log('---------generateTicketQrByZone------------');
    return this.cNftTempsService.generateTicketQrByZone(payload, res);
  }

  @Post('/generate-ticket-qr-by-event-id')
  @UseGuards(InternalAPIGuard)
  generateTicketQrByEventID(
    @Body() payload,
    @Res() res: Response,
  ): Promise<any> {
    console.log('---------generateTicketQrByEventID------------');
    return this.cNftTempsService
      .generateTicketQrByEventID(payload, res)
      .then((response) => {
        if (response === undefined) {
          return RestAPI.badRequest(res, 'ERROR');
        } else {
          if (response.result) {
            return RestAPI.success(res, response.data);
          } else {
            return RestAPI.badRequest(res, response.data);
          }
        }
      });
  }

  @Post('/check-address')
  async checkAddress(@Body() payload, @Res() res: Response): Promise<any> {
    const address = payload.mint_address ?? '';
    if (address) {
      const nft = await this.cNftTempsService.checkAddress(address);
      return RestAPI.success(res, nft);
    }
    return RestAPI.forbidden(res, 'Invalid address');
  }
}
