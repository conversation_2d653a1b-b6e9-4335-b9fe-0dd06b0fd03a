import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

@Command({
  name: 'process-ticket-qr-mint',
  description: 'Process generate transactions',
})
@Injectable()
export class GenerateTicketQrMintAddressCommand extends CommandRunner {
  private readonly logger = new Logger(GenerateTicketQrMintAddressCommand.name);
  private batchSize = 10; // Default batch size
  private timeDelayNextZone = 5 * 1000; // 5 seconds

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Thứ tự chạy:
  // B1: Sẽ tạo ra toàn bộ vé theo từng seat / zone / stage date / stage / event vào bảng cnft_temp
  // VD: npx ts-node src/cli.ts process-ticket-qr-mint --event_id="31ecdd58-0634-4e4b-9004-418a5a8e3e6c" --event_stage_id=28 --event_date_id=37
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const limit = options?.batchSize || this.batchSize;
    const event_id = options?.event_id;
    const event_stage_id = options?.event_stage_id;
    const event_date_id = options?.event_date_id;
    const event_zone_detail_id = options?.event_zone_detail_id;

    console.info({
      limit,
      event_id,
      event_stage_id,
      event_date_id,
      event_zone_detail_id,
    });

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    const getEventZoneIds = await firstValueFrom(
      this.httpService.get(
        `${app_domain}/_api/events/get-list-event-date-by-zone?event_date_id=${event_date_id}`,
        { headers },
      ),
    );
    const listEventZoneIds = getEventZoneIds.data.data || [];
    console.log('---listEventZoneIds :>> ', listEventZoneIds);
    if (listEventZoneIds.length === 0) {
      console.info('No event zone found');
      return;
    }
    console.log('Begin generate');

    for (const eventZoneId of listEventZoneIds) {
      const payload = {
        event_id,
        event_stage_id,
        event_date_id,
        event_zone_detail_id: eventZoneId,
      };
      const generateTicketsByZone = await firstValueFrom(
        this.httpService.post(
          `${app_domain}/_api/cnft/generate-ticket-qr-by-zone`,
          payload,
          { headers },
        ),
      );
      console.log('generateTicketsByZone :>> ', generateTicketsByZone.data);
      console.log('payload to generate: ', payload);
      console.info(
        '--------Generate ticket success---------',
        generateTicketsByZone.data,
      );
      await this.delayTime(this.timeDelayNextZone);
    }
    console.info('<<<<<<<<<<<All tickets have been created.>>>>>>>>>>>>>');
  }

  @Option({
    flags: '-b, --batchSize <batchSize>',
    description: 'Batch size for processing',
  })
  parseBatchSize(val: string): number {
    return parseInt(val, this.batchSize);
  }

  @Option({
    flags: '-event_id, --event_id <event_id>',
  })
  parseEventId(val: string): string {
    return val;
  }

  @Option({
    flags: '-event_stage_id, --event_stage_id <event_stage_id>',
  })
  parseEventStageId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_date_id, --event_date_id <event_date_id>',
  })
  parseEventDateId(val: string): number {
    return parseInt(val);
  }

  @Option({
    flags: '-event_zone_detail_id, --event_zone_detail_id <event_date_id>',
  })
  parseEventZoneDetailId(val: string): number {
    return parseInt(val);
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }
}
