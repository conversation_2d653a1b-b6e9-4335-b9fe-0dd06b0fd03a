import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/sequelize';
import { PaymentDetail } from '@payment_details/models/payment_detail.model';
import Common from 'common/common';
import _ from 'lodash';
import config from 'config/setting';
import ActivityPdf from 'common/activity_pdf';
import nodemailer from 'nodemailer';
import moment from 'moment';
import * as path from 'path';
import * as ejs from 'ejs';
import { Event } from '@events/models/event.model';
import { User } from '@users/models/user.model';
import { EventStagesService } from '@event_stages/event_stages.services';
import { EventStageDatesService } from '@event_stage_dates/event_stage_dates.services';
import { EventZoneDetailsService } from '@event_zone_details/event_zone_details.services';
import { ParticipantInfoService } from '@participants_info/participants_info.services';
import { CouponsService } from '@coupons/coupons.services';
import { TYPE_PAYMENT } from 'config/constant';
import Constants from 'common/constants';
import { TempSeatsService } from '@temp_seats/temp_seats.services';
import { TempQuantitiesService } from '@temp_quantities/temp_quantities.services';
import { TempSeat } from '@temp_seats/models/temp_seat.model';
import { TempQuantity } from '@temp_quantities/models/temp_quantity.model';
import { CNftTempsService } from '@cnft_temps/cnft_temps.services';
import { CNftTemp } from '@cnft_temps/models/cnft_temp.model';
import { CreationAttributes, Op } from 'sequelize';
import { SchedulerRegistry } from '@nestjs/schedule';
import { UserHasVoucherService } from '@user_has_vouchers/user_has_vouchers.services';
import { Transaction } from '@transactions/models/transaction.model';
import { EventSeatPricesService } from '@event_seat_prices/event_seat_prices.services';
import { TelegramService } from 'core/telegram/telegram.service';

@Injectable()
export class SendMailTicketService {
  constructor(
    @InjectModel(PaymentDetail)
    private readonly paymentDetailModel: typeof PaymentDetail,
    @InjectModel(Event)
    private readonly eventModel: typeof Event,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(TempSeat)
    private readonly tempSeatModel: typeof TempSeat,
    @InjectModel(TempQuantity)
    private readonly tempQuantityModel: typeof TempQuantity,
    @InjectModel(CNftTemp)
    private readonly cNftTempModel: typeof CNftTemp,
    @InjectModel(Transaction)
    private readonly transactionModel: typeof Transaction,

    private eventStageService: EventStagesService,
    private eventStageDateService: EventStageDatesService,
    private eventZoneDetailService: EventZoneDetailsService,
    private couponsService: CouponsService,
    private participantInfoService: ParticipantInfoService,
    private tempSeatsService: TempSeatsService,
    private tempQuantitiesService: TempQuantitiesService,
    private cNftTempsService: CNftTempsService,
    private schedulerRegistry: SchedulerRegistry,
    private userHasVoucherService: UserHasVoucherService,
    private eventSeatPricesService: EventSeatPricesService,
    private telegramService: TelegramService,
  ) {}

  private readonly logger = new Logger(SendMailTicketService.name);

  @Cron('0 */1 * * * *') // every 15 minutes
  // @Cron(CronExpression.EVERY_10_MINUTES)
  async handleCron() {
    this.logger.debug('Task >>>> Send mail ticket every 15 minutes');
    let listPayment = await this.paymentDetailModel.findAll({
      where: {
        status_payment: true,
        status_sent_mail: false,
      },
      raw: true,
    });

    // Prevent send mail to all user
    if (process.env.NODE_ENV !== 'production') {
      listPayment = listPayment.filter((item) =>
        ['<EMAIL>', '<EMAIL>'].includes(
          item.email,
        ),
      );
    }

    if (!_.isEmpty(listPayment)) {
      for (let index = 0; index < listPayment.length; index++) {
        const item = listPayment[index];

        const event = await this.eventModel.findOne({
          where: {
            active: true,
            deleted: false,
            id: item.event_id,
          },
          raw: true,
        });

        const userInfo: any = await this.userModel.findOne({
          where: {
            id: item.user_id,
            deleted: false,
          },
          raw: true,
        });
        const event_stage = await this.eventStageService.getEventStageDetail(
          item.event_stage_id,
        );

        const event_date =
          await this.eventStageDateService.getEventStageDateDetail(
            item.event_stage_date_id,
          );

        let zone_name = '';
        let zone_names = {};
        let new_event_obj = item.event_obj;
        if (item.type_payment === TYPE_PAYMENT.QUANTITY) {
          const event_zone_detail_ids = item.event_zone_detail_id.split(',');
          const event_zone_detail_infos: any = [];
          for (const event_zone_detail_id of event_zone_detail_ids) {
            event_zone_detail_infos.push(
              await this.eventZoneDetailService.getEventZoneDetail(
                Number(event_zone_detail_id),
              ),
            );
          }
          zone_names = event_zone_detail_infos.reduce((acc, item) => {
            acc[item.id] = item.zone_name;
            return acc;
          }, {});
          zone_name = Object.values(zone_names).join(', ');

          /**
           * Get Mint cNFT case quantity
           */
          console.log('=======+> status_mint_nft: ', item.status_mint_nft);
          if (!item.status_mint_nft) {
            const listTempQuantitiesUpdate: any = [];
            const listTempQuantities =
              await this.tempQuantitiesService.getListTempQuantityByTransactionId(
                item.transaction_id,
                item.date_use,
              );
            for (const tempQuantityItem of listTempQuantities) {
              if (!tempQuantityItem.mint_address) {
                const { mint_address, nft_transaction, nft_code_order } =
                  await this.getcNFTInfo({
                    item: tempQuantityItem,
                    event,
                    event_date,
                  });
                const tempQuantityUpdate: Partial<TempQuantity> = {
                  mint_address,
                  nft_transaction,
                  nft_code_order,
                  is_invoice: true,
                };
                console.log('=======> tempQuantityUpdate', tempQuantityUpdate);
                await this.tempQuantityModel
                  .update(tempQuantityUpdate, {
                    where: { id: tempQuantityItem.id },
                  })
                  .then((result) => {
                    console.log(
                      '===========> await this.tempQuantityModel.update >>> success ',
                    );
                  })
                  .catch((error) => {
                    console.log(
                      '===========> await this.tempQuantityModel.update >>> error ',
                      error.message,
                    );
                  });
                listTempQuantitiesUpdate.push({
                  ...tempQuantityItem,
                  ...tempQuantityUpdate,
                });
              }
            }
            new_event_obj = !_.isEmpty(listTempQuantitiesUpdate)
              ? listTempQuantitiesUpdate
              : item.event_obj;
          }
          /**
           * End Get Mint cNFT case quantity
           */
        } else {
          const event_zone_detail_info =
            await this.eventZoneDetailService.getEventZoneDetail(
              item.event_zone_detail_id,
            );
          zone_name = `${event_zone_detail_info?.zone_title && event_zone_detail_info?.zone_title.length > 0 ? `${event_zone_detail_info?.zone_title} / ` : ''}${event_zone_detail_info?.zone_name}`;
          const isTableCircle =
            event_zone_detail_info?.attributes?.group === 'table_circle'
              ? true
              : false;

          /**
           * Get Mint cNFT case seat map
           */
          if (!item.status_mint_nft) {
            const listTempSeatsUpdate: any = [];
            const listTempSeats =
              await this.tempSeatsService.getListTempSeatByTransactionId(
                item.transaction_id,
                item.date_use,
              );
            for (const tempSeatItem of listTempSeats) {
              if (!tempSeatItem.mint_address) {
                const { mint_address, nft_transaction, nft_code_order } =
                  await this.getcNFTInfo({
                    item: { ...tempSeatItem, isTableCircle: isTableCircle },
                    event,
                    event_date,
                  });
                const tempSeatUpdate: Partial<TempSeat> = {
                  mint_address,
                  nft_transaction,
                  nft_code_order,
                  is_invoice: true,
                };
                await this.tempSeatModel
                  .update(tempSeatUpdate, {
                    where: { id: tempSeatItem.id },
                  })
                  .then((result) => {
                    console.log(
                      '===========> await this.tempSeatModel.update >>> success ',
                    );
                  })
                  .catch((error) => {
                    console.log(
                      '===========> await this.tempSeatModel.update >>> error ',
                      error.message,
                    );
                  });
                listTempSeatsUpdate.push({
                  ...tempSeatItem,
                  ...tempSeatUpdate,
                });
              }
            }
            new_event_obj = !_.isEmpty(listTempSeatsUpdate)
              ? listTempSeatsUpdate
              : item.event_obj;
          }
          /**
           * End Get Mint cNFT case seat map
           */
        }

        // send mail
        const banner_vi = event_date?.email_poster_vi
          ? `https://vtix.vn/${Constants.EVENT_GROUP_DEFINED[event?.event_group_id || 1].slug}/${event_date?.email_poster_vi}`
          : event?.email_poster_vi
            ? `https://vtix.vn/${Constants.EVENT_GROUP_DEFINED[event?.event_group_id || 1].slug}/${event?.email_poster_vi}`
            : `https://vtix.vn/${Constants.EVENT_GROUP_DEFINED[event?.event_group_id || 1].slug}/${event?.poster_vi}`;

        const banner_en = event_date?.email_poster_en
          ? `https://vtix.vn/${Constants.EVENT_GROUP_DEFINED[event?.event_group_id || 1].slug}/${event_date?.email_poster_en}`
          : event?.email_poster_en
            ? `https://vtix.vn/${Constants.EVENT_GROUP_DEFINED[event?.event_group_id || 1].slug}/${event?.email_poster_en}`
            : `https://vtix.vn/${Constants.EVENT_GROUP_DEFINED[event?.event_group_id || 1].slug}/${event?.poster_en}`;

        let event_poster =
          userInfo.id_country == config.id_vietnam_country
            ? banner_vi
            : banner_en;
        event_poster = await Common.convertImageToBase64(event_poster).then(
          async (base64) => {
            return base64;
          },
        );

        // Lấy thông tin người tham gia giải chạy
        let participants_info = {};
        if (
          event_date &&
          event_date.attributes &&
          JSON.parse(event_date.attributes?.participants_form ?? false) == true
        ) {
          participants_info =
            await this.participantInfoService.getListParticipantInfo({
              transaction_id: item.transaction_id,
            });
        }

        const event_obj: any = item.event_obj;
        const seat_name = event_obj.map((item) => item.seat_code).join(', ');
        let coupon_detail: any = null;
        if (item.coupon_id) {
          coupon_detail = await this.couponsService.getCouponDetail(
            item.coupon_id,
          );
        }

        const data = {
          event_poster,
          paymentDetail: {
            ...item,
            event_obj: new_event_obj,
          },
          event,
          event_stage,
          event_stage_date: event_date,
          zone_names,
          zone_name,
          totalPrice: item.total_price,
          discountedPrice: item.discounted_price,
          comPrice: item.total_price - item.discounted_price,
          userinfo: { ...userInfo, lastName: userInfo.last_name },
          seat_name,
          coupon_detail,
          participants_info,
          show_ticket_seat: _.get(event?.attributes, 'show_ticket_seat', true),
          show_hour_event: _.get(event?.attributes, 'show_hour', true),
        };
        if (userInfo.id_country == config.id_vietnam_country) {
          await this.sendMail({
            template: 'mail/order_success_vi_v2',
            data,
            email: item.email,
          });
        } else {
          await this.sendMail({
            template: 'mail/order_success_en_v2',
            data,
            email: item.email,
          });
        }

        const dataUpdatePaymentDetail: Partial<PaymentDetail> = {
          status_sent_mail: true,
          status_mint_nft: true,
        };

        await this.paymentDetailModel
          .update(
            {
              ...dataUpdatePaymentDetail,
              event_obj: new_event_obj,
            },
            {
              where: { id: item.id },
            },
          )
          .then(
            async () =>
              await this.transactionModel
                .update(
                  {
                    is_invoice: true,
                  },
                  {
                    where: {
                      id: item.transaction_id,
                    },
                  },
                )
                .then(async () => {
                  // Update seat used table event_seat_price
                  const event_stage_ids = _.countBy(
                    new_event_obj,
                    'event_seat_id',
                  );
                  if (!_.isEmpty(event_stage_ids)) {
                    for (const event_stage_id in event_stage_ids) {
                      await this.eventSeatPricesService.updateEventSeatQuantity(
                        Number(event_stage_id),
                        event_stage_ids[event_stage_id],
                      );
                    }
                  }

                  // Update status thông tin của người tham gia giải chạy
                  if (
                    event_date &&
                    event_date.attributes &&
                    JSON.parse(
                      event_date.attributes?.participants_form ?? false,
                    ) == true
                  ) {
                    await this.participantInfoService.updateStatusParticipantInfo(
                      item.transaction_id,
                      true,
                    );
                  }

                  // Gift voucher if event has voucher
                  if (event?.attributes && event?.attributes?.voucher_ids) {
                    console.info(
                      '>>>>>>> Processing SEND voucher gift for event',
                      event?.id,
                    );
                    await this.scheduleSendMailGiftVoucher(event, item);
                  }

                  // Gửi thông tin đến telegram bot
                  if (process.env.NODE_ENV === 'production') {
                    await this.telegramService
                      .sendPaymentNotification({
                        eventName: event?.name_vi ?? '',
                        orderCode: item.code,
                        seats: Array.isArray(item.event_obj)
                          ? item.event_obj.map((v: any) => v.seat_code)
                          : [],
                        totalPrice: item.discounted_price.toString(),
                        statusPayment: true,
                        userName: item.user_name,
                        phone: item.phone,
                        email: item.email,
                      })
                      .then(() => {
                        console.log('============= send telegram success');
                      })
                      .catch((error) => {
                        console.log('============= send telegram error', error);
                      });
                  }
                })
                .catch((errorUpdateSeat) => {
                  console.log(
                    '============= Error Update Seat => success func',
                    errorUpdateSeat,
                  );
                }),
          );

        this.logger.log('UPDATE PAYMNENT SUCCESS');
      }
    } else {
      this.logger.debug('NOTHING PAYMENT TO SEND');
    }
  }

  async sendMail({ template, data, email }) {
    const payment_detail = data.paymentDetail;
    const event_stage_date = data.event_stage_date;
    const notify_email = event_stage_date.notify_email ?? [];
    let textDateShow = '';
    if (event_stage_date?.is_unlimited && event_stage_date.is_unlimited) {
      textDateShow = 'Vé vô thời hạn / Unlimited ticket';
    } else {
      textDateShow = `${moment(event_stage_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format(`DD/MM/YYYY hh:mm`)} ${moment(event_stage_date.show_date * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format('a')
        .toUpperCase()}`;

      if (!data.show_hour_event) {
        textDateShow = `${moment(event_stage_date.show_date * 1000)
          .tz('Asia/Ho_Chi_Minh')
          .format(`DD/MM/YYYY`)
          .toUpperCase()}`;
      }
    }
    console.log('func ======> sendMail 1', textDateShow);
    if (
      event_stage_date?.short_desc_vi &&
      event_stage_date.short_desc_vi.length > 0
    ) {
      textDateShow = `${textDateShow}\n${event_stage_date.short_desc_vi}`;
    }
    console.log('func ======> sendMail 2', textDateShow);
    if (
      event_stage_date?.short_desc_en &&
      event_stage_date.short_desc_en.length > 0
    ) {
      textDateShow = `${textDateShow}\n${event_stage_date.short_desc_en}`;
    }
    console.log('func ======> sendMail 3', textDateShow);

    if (payment_detail.date_use > 0) {
      textDateShow = `${moment(payment_detail.date_use * 1000)
        .tz('Asia/Ho_Chi_Minh')
        .format(`DD/MM/YYYY`)}`;
      if (event_stage_date.attributes?.text_dropdown_date) {
        textDateShow = `${textDateShow} | ${event_stage_date.attributes?.text_dropdown_date || ''}`;
      } else if (event_stage_date.attributes?.text_show_time) {
        textDateShow = `${textDateShow} | ${event_stage_date.attributes?.text_show_time || ''}`;
      }
    } else {
      if (event_stage_date.attributes?.text_show_time) {
        textDateShow = event_stage_date.attributes.text_show_time;
      }
    }
    console.log('func ======> sendMail 4 - ', textDateShow);

    // Text hiển thị loại vé / hạng vé / hạng ghế,...
    let textShowTypeTicket = 'Hạng vé/Zone';
    if (
      data.event.attributes?.expect_text &&
      data.event.attributes.expect_text === 'seat'
    ) {
      textShowTypeTicket = 'Hạng ghế/Zone';
    }
    // End

    const receiver_user_name = data.paymentDetail.user_name;
    const receiver_first_name = !_.isEmpty(receiver_user_name)
      ? Common.splitFullName(receiver_user_name).firstName
      : data.userinfo.name;
    const receiver_last_name = !_.isEmpty(receiver_user_name)
      ? Common.splitFullName(receiver_user_name).lastName
      : data.userinfo.lastName;

    // chỉ dùng 2 loại ticket và seat
    _.assign(data.paymentDetail, {
      textType:
        _.get(data.event.attributes, 'expect_text', 'ticket') === 'seat'
          ? 'seat'
          : 'ticket',
    });

    const convertData = {
      moment: moment,
      ...data,
      user_buy_info: {
        first_name: receiver_first_name,
        last_name: receiver_last_name,
        full_name: receiver_user_name,
        email: data.paymentDetail.email,
        phone: data.paymentDetail.phone,
      },
      event_stage_date: {
        ...event_stage_date,
        textDateShow: textDateShow,
      },
      seat_name: `${data.zone_name}-${data.seat_name.length > 1 ? data.seat_name : `0${data.seat_name}`}`,
      show_ticket_seat: data.show_ticket_seat,
      textShowTypeTicket: textShowTypeTicket,
    };

    try {
      // Chỉ có admin lấy file vé và gửi cho khách hàng.
      // trường hợp này chỉ dùng trong những trường hợp đặc biệt.
      //   await ActivityPdf.createPdfV2(null, convertData)
      //     .then(async (attachments) => {
      //       console.log('>>>>>>>> attachments', attachments);
      //       await this.executeSendMail({
      //         template,
      //         convertData,
      //         email: '<EMAIL>',
      //         attachments,
      //       }).then(async () => {});
      //     })
      //     .catch((error) => {
      //       console.log('schedule > sendMail > attachments', error);
      //     });
      // Kết thúc

      // Đây là flow gửi mail một cách bình thường
      // gửi lại email cho những khách hàng đã đặt thành công và notify cho admin / NTC
      await ActivityPdf.createPdfV2(null, convertData)
        .then(async (attachments) => {
          console.log('>>>>>>>> attachments', attachments);
          await this.executeSendMail({
            template,
            convertData,
            email,
            attachments,
          }).then(async () => {
            // Logic gửi mail đến người nhận thông tin
            if (notify_email.length > 0) {
              let onlyAdminNotify = notify_email;

              if (
                process.env.NODE_ENV === 'staging' ||
                process.env.NODE_ENV === 'development'
              ) {
                onlyAdminNotify = [
                  '<EMAIL>',
                  '<EMAIL>',
                ];
              }

              for (const email_partner of onlyAdminNotify) {
                await this.executeSendMail({
                  template: 'mail/notify_order_success_vi',
                  convertData,
                  email: email_partner,
                  attachments,
                });
              }
            }
          });
        })
        .catch((error) => {
          console.log('schedule > sendMail > attachments', error);
        });
      return;
    } catch (error) {
      console.log('Lỗiiii', error);
    }
  }

  async executeSendMail({ template, convertData, email, attachments }) {
    const transporter = nodemailer.createTransport({
      service: config.config_email.service,
      port: config.config_email.port,
      host: config.config_email.host,
      secure: true,
      pool: true,
      auth: {
        user: config.config_email.email_user,
        pass: config.config_email.email_password,
      },
    });
    const templatePath = path.join(__dirname, '..', 'views', `${template}.ejs`);
    await ejs.renderFile(templatePath, convertData, async function (err, html) {
      if (err) {
        console.log('Error rendering template:', err);
        return;
      }
      const mailOptions = {
        from: config.config_email.email_sender,
        to: email,
        subject: `Order Confirmation / Xác nhận thanh toán cho đơn hàng ${convertData.paymentDetail.code}`,
        html: html,
        attachments: attachments,
      };
      await transporter.sendMail(mailOptions, function (err, info) {
        if (err) {
          console.log('SEND MAIL FAIL');
          console.log(err);
        } else {
          console.log('SEND MAIL SUCCESS TO', email);
        }
      });
    });
  }

  async getcNFTInfo({ item, event, event_date }) {
    const whereNFT = {
      deleted: false,
      event_id: item.event_id,
      event_stage_id: item.event_stage_id,
      event_date_id: item.event_stage_date_id,
      event_zone_detail_id: item.event_zone_detail_id,
      event_seat_id: item.event_seat_id,
      pos: item.seat_number,
      mint_address: {
        [Op.not]: null,
      },
      cart_detail_temp_id: '0',
    };
    if (item.isTableCirle) {
      _.assign(whereNFT, { seat_name: item.seat_code });
    }
    console.log('========> getcNFTInfo >> condition >> whereNFT', whereNFT);
    const cnft_detail = await this.cNftTempsService.getCnftTempDetail(whereNFT);
    console.log('========> getcNFTInfo >> cnft_detail data', cnft_detail);
    if (!Common.isEmpty(cnft_detail) && cnft_detail) {
      await this.cNftTempModel
        .update(
          {
            cart_detail_temp_id: item.id,
          },
          {
            where: { id: cnft_detail.id },
          },
        )
        .then((result) => {
          console.log(
            '=======> await this.cNftTempModel.update =====> success',
          );
        })
        .catch((error) => {
          console.log(
            '=======> await this.cNftTempModel.update',
            error.message,
          );
        });

      return {
        mint_address: cnft_detail.mint_address,
        nft_transaction: cnft_detail.transaction,
        nft_code_order: cnft_detail.code_order,
      };
    } else {
      if (event_date.ticket_type !== 'qr') {
        return {
          mint_address: null,
          nft_transaction: null,
          nft_code_order: null,
        };
      }

      // Find any existing NFT to clone properties
      const existingNFT = await this.cNftTempModel.findOne({
        where: {
          deleted: false,
          event_id: item.event_id,
          event_stage_id: item.event_stage_id,
          event_zone_detail_id: item.event_zone_detail_id,
          event_seat_id: item.event_seat_id,
          pos: item.seat_number,
          mint_address: {
            [Op.not]: null,
          },
        },
        raw: true,
      });

      const prefixCode = event.code;
      const code_order = prefixCode + Common.getRandomCode(10);
      const mint_address = this.cNftTempsService.generateRandomMintAddress();
      const transaction = this.cNftTempsService.generateRandomTransaction();
      console.log('---------getcNFTInfo', mint_address);

      const newcNFT = await this.cNftTempModel
        .create({
          ...(existingNFT
            ? _.omit(existingNFT, [
                'id',
                'mint_address',
                'transaction',
                'code_order',
                'cart_detail_temp_id',
              ])
            : {}),
          event_date_id: item.event_stage_date_id,
          mint_address,
          transaction,
          code_order,
          cart_detail_temp_id: item.id,
        } as CreationAttributes<CNftTemp>)
        .then((newData: any) => {
          return newData;
        })
        .catch((errorInsertNewData) => {
          console.log('---------errorInsertNewData', errorInsertNewData);
          // event_id,event_stage_id,event_date_id,event_zone_detail_id,custom_ticket_id,event_seat_id,seat_name,pos,row_code
          // unique_event_seat_constraint
        });

      return {
        mint_address: newcNFT.mint_address,
        nft_transaction: newcNFT.transaction,
        nft_code_order: newcNFT.code_order,
      };
    }
  }

  private scheduleSendMailGiftVoucher(event: any, paymentDetail: any): void {
    const currentTime = parseInt(moment(new Date()).format('X'));

    const waitTimeInMs = 5 * 60 * 1000; // 5 minutes

    const timeout = setTimeout(async () => {
      await this.executeSendMailGiftVoucher({
        event,
        paymentDetail,
        timeKey: currentTime,
      });
    }, waitTimeInMs);

    // Register the timeout with a unique name so it can be managed
    const checkSchedule: boolean = this.schedulerRegistry.doesExist(
      'timeout',
      `send_mail_gift_voucher_user_id_${paymentDetail.user_id}_transaction_${paymentDetail.transaction_id}_time_${currentTime}`,
    );
    if (!checkSchedule) {
      this.schedulerRegistry.addTimeout(
        `send_mail_gift_voucher_user_id_${paymentDetail.user_id}_transaction_${paymentDetail.transaction_id}_time_${currentTime}`,
        timeout,
      );
    }
  }

  async executeSendMailGiftVoucher({ event, paymentDetail, timeKey }) {
    const voucherIds = event?.attributes?.voucher_ids;
    if (voucherIds.length > 0) {
      try {
        for (const voucherId of voucherIds) {
          await this.userHasVoucherService.assignVoucher({
            user_id: paymentDetail.user_id,
            voucher_id: voucherId,
            status: 'active',
          });
        }
      } catch (error) {
        console.error(
          'Error processing SEND voucher gift for event',
          event?.id,
          error.message,
        );
      } finally {
        // Clean up the timeout from the registry
        const checkSchedule: boolean = this.schedulerRegistry.doesExist(
          'timeout',
          `send_mail_gift_voucher_user_id_${paymentDetail.user_id}_transaction_${paymentDetail.transaction_id}_time_${timeKey}`,
        );
        if (checkSchedule) {
          this.schedulerRegistry.deleteTimeout(
            `send_mail_gift_voucher_user_id_${paymentDetail.user_id}_transaction_${paymentDetail.transaction_id}_time_${timeKey}`,
          );
        }
      }
    }
  }
}
