import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { EventSeatPrice } from './models/event_seat_price.model';

@Injectable()
export class EventSeatPricesService {
  constructor(
    @InjectModel(EventSeatPrice)
    private readonly eventSeatPriceModel: typeof EventSeatPrice,

    private sequelize: Sequelize,
  ) {}

  async getEventSeatPriceDetail(event_seat_price_id) {
    return await this.eventSeatPriceModel.findOne({
      where: {
        active: true,
        deleted: false,
        id: event_seat_price_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- <PERSON> is here
    });
  }

  async getListEventSeatPrice(event_zone_detail_id) {
    return await this.eventSeatPriceModel.findAll({
      where: {
        active: true,
        deleted: false,
        zone_detail_id: event_zone_detail_id,
      },
      raw: true, // <----------- Magic is here
      nest: true, // <----------- Magic is here
    });
  }

  async updateEventSeatQuantity(event_seat_price_id, quantity) {
    return await this.eventSeatPriceModel.update(
      { seat_used: this.sequelize.literal(`seat_used + ${quantity}`) },
      { where: { id: event_seat_price_id } },
    );
  }
}
