import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { VoucherProviderService } from './voucher_providers.services';
import { VoucherProvider } from './models/voucher_provider.model';
import { Voucher } from 'core/vouchers/models/voucher.model';

@Module({
  imports: [SequelizeModule.forFeature([VoucherProvider, Voucher])],
  providers: [VoucherProviderService],
  exports: [VoucherProviderService],
})
export class VoucherProvidersModule { }
