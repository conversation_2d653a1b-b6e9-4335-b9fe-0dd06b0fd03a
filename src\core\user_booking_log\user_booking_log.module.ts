import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UserBookingLogService } from './user_booking_log.services';
import { UserBookingLog } from './models/user_booking_log.model';
import { UserBookingLogController } from './user_booking_log.controller';
import { EventStage } from '@event_stages/models/event_stage.model';
import { EventStageDate } from '@event_stage_dates/models/event_stage_date.model';
import { EventZoneDetail } from '@event_zone_details/models/event_zone_detail.model';
import { EventsModule } from '@events/events.module';
import { EventStagesModule } from '@event_stages/event_stages.module';
import { EventStageDatesModule } from '@event_stage_dates/event_stage_dates.module';
import { EventZoneDetailsModule } from '@event_zone_details/event_zone_details.module';
import { EventSeatPricesModule } from '@event_seat_prices/event_seat_prices.module';
import { User } from '@users/models/user.model';
import { UsersModule } from '@users/users.module';
import { Event } from '@events/models/event.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      UserBookingLog,
      User,
      Event,
      EventStage,
      EventStageDate,
      EventZoneDetail,
    ]),
    UsersModule,
    EventsModule,
    EventStagesModule,
    EventStageDatesModule,
    EventZoneDetailsModule,
    EventSeatPricesModule,
  ],
  providers: [UserBookingLogService],
  controllers: [UserBookingLogController],
  exports: [UserBookingLogService],
})
export class UserBookingLogModule {}
