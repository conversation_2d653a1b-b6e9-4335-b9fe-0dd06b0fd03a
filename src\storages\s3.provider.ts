import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StoragesService } from './storages.service';
import { Bucket, getBucketName } from '../utils/buckets';
import { S3, Credentials } from 'aws-sdk';
import { SIGNED_URL_EXPIRES } from 'config/constant';

@Injectable()
export class S3Provider implements StoragesService {
  private client: S3;
  private signedUrlExpires: number;
  private readonly logger = new Logger(S3Provider.name);

  constructor(private configService: ConfigService) {
    const awsConfig = {
      region: configService.get('file.awsS3Region', { infer: true }),
      credentials: new Credentials({
        accessKeyId: configService.getOrThrow('file.accessKeyId', {
          infer: true,
        }),
        secretAccessKey: configService.getOrThrow('file.secretAccessKey', {
          infer: true,
        }),
      }),
    };
    this.client = new S3(awsConfig);
    this.signedUrlExpires = SIGNED_URL_EXPIRES;
  }

  async upload(
    bucket: Bucket,
    key: string,
    data: Buffer,
    contentType: string,
  ): Promise<void> {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }
    const params: S3.PutObjectRequest = {
      Bucket: bucketName,
      Key: key,
      Body: data,
      ContentType: contentType,
    };

    await this.client.upload(params).promise();
  }

  async download(bucket: Bucket, key: string): Promise<Buffer> {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }
    const params: S3.GetObjectRequest = {
      Bucket: bucketName,
      Key: key,
    };

    const response = await this.client.getObject(params).promise();
    return response.Body as Buffer;
  }

  async delete(bucket: Bucket, key: string): Promise<void> {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }
    const params: S3.DeleteObjectRequest = {
      Bucket: bucketName,
      Key: key,
    };

    await this.client.deleteObject(params).promise();
  }

  getSignedUrl(bucket: Bucket, key: string, contentType?: string): string {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }
    console.log(
      '----------------getSignedUrl',
      bucketName,
      key,
      this.signedUrlExpires,
    );
    return this.client.getSignedUrl('putObject', {
      Bucket: bucketName,
      Key: key,
      Expires: this.signedUrlExpires,
      ContentType: contentType || 'application/octet-stream',
    });
  }

  getUploadSignedUrl(bucket: Bucket, key: string): string {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }
    const params: S3.PresignedPost.Params = {
      Bucket: bucketName,
      Fields: {
        key,
      },
      Expires: this.signedUrlExpires,
    };
    return this.client.createPresignedPost(params).url;
  }

  async exists(bucket: Bucket, objectName: string): Promise<boolean> {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }
    const params: S3.HeadObjectRequest = {
      Bucket: bucketName,
      Key: objectName,
    };

    try {
      await this.client.headObject(params).promise();
      return true; // Object exists
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      throw error;
    }
  }

  async deletePath(bucket: Bucket, path: string): Promise<void> {
    const bucketName = getBucketName(bucket);
    if (!bucketName) {
      throw new Error('invalid bucket name');
    }

    const listParams: S3.ListObjectsV2Request = {
      Bucket: bucketName,
      Prefix: path.endsWith('/') ? path : `${path}/`, // Ensure path ends with a slash
    };

    const deleteObjects = async (objects: S3.ObjectList) => {
      if (objects.length === 0) return;

      const deleteParams: S3.DeleteObjectsRequest = {
        Bucket: bucketName,
        Delete: {
          Objects: objects.map(({ Key }) => ({ Key: Key ?? '' })),
          Quiet: true,
        },
      };

      await this.client.deleteObjects(deleteParams).promise();
    };

    let isTruncated: boolean = true;
    while (isTruncated) {
      const listedObjects = await this.client
        .listObjectsV2(listParams)
        .promise();
      await deleteObjects(listedObjects.Contents || []);

      isTruncated = listedObjects.IsTruncated ?? true;
      if (isTruncated) {
        listParams.ContinuationToken = listedObjects.NextContinuationToken;
      }
    }
  }
}
