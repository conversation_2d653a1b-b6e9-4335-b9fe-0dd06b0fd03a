import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import config from '../config/setting';
import * as path from 'path';
import * as ejs from 'ejs';
import nodemailer from 'nodemailer';
import { firstValueFrom } from 'rxjs';
import _ from 'lodash';

@Command({
  name: 'process-send-pin-event-marketing',
  description:
    'Process send email marketing with customize templated email for event/workshop,....',
})
/**
 * Gửi email marketing
 * chỉ định chính xác template cần gửi tại thư mục views/mail/marketing/...
 */
@Injectable()
export class SendPinEventMarketingCommand extends CommandRunner {
  private readonly logger = new Logger(SendPinEventMarketingCommand.name);
  private timeDelayNextZone = 5 * 1000; // 5 seconds

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  // Guide:
  // VD: npx ts-node src/cli.ts process-send-pin-event-marketing --template="pin_event" --approve=true
  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    const template = options?.template;
    const approve = options?.approve;
    console.info({
      message: 'Call email template: ',
      template,
      approve,
    });

    const defineData: any = {
      special: {
        image:
          'https://vtix.vn/event/meo-hoang/10-cach-gap-ma-loi-nguyen-cua-hieu-ky/banner-de.webp',
        title: '10 CÁCH GẶP MA - LỜI NGUYỀN CỦA HIẾU KỲ',
        address: '59 Cao Thắng, P.3, Q.3, Tp.HCM',
        fullDate: 'Thứ bảy, 8 tháng 3 2025 19:30',
        description:
          '<p><strong style="font-weight: 700;">“10 CÁCH GẶP MA – LỜI NGUYỀN CỦA HIẾU KỲ”</strong> 🤗 vở diễn hấp dẫn & mới kén của Mèo Hoang năm 2025.</p><p>Những bí mật của thế giới tâm linh luôn khiến cho chúng ta tò mò, hiếu kỳ và muốn trải nghiệm cảm giác hồi hộp và kỳ bí để tìm ra sự thật, những câu chuyện ẩn giấu bấy lâu...</p>',
        link: 'https://www.vtix.vn/vi/event/bong-hoa-dep-nhat',
        coupon: 'DISCOUNT20',
        discount: '20%',
        couponDes: 'Giảm lên đến',
      },
      list: [
        {
          image:
            'https://vtix.vn/event/unicorn/bong-hoa-dep-nhat/banner-mb.webp',
          title: 'Bông Hoa Đẹp Nhất 1',
          date: '20.01.2025',
          hour: '19:00',
          address:
            'Bãi đá sông Hồng, Hà Nội (cuối ngõ 264 Âu Cơ, Quận Tây Hồ, TP. Hà Nội)',
          fullDate: 'Thứ năm, 6 tháng 3 2025 19:00',
          link: 'https://www.vtix.vn/vi/event/bong-hoa-dep-nhat',
          coupon: 'GROUP011',
          discount: '20%',
          couponDes: 'Giảm lên đến',
        },
        {
          image:
            'https://vtix.vn/event/unicorn/bong-hoa-dep-nhat/banner-mb.webp',
          title: 'Bông Hoa Đẹp Nhất 2',
          date: '20.01.2025',
          hour: '19:00',
          address:
            'Bãi đá sông Hồng, Hà Nội (cuối ngõ 264 Âu Cơ, Quận Tây Hồ, TP. Hà Nội)',
          fullDate: 'Thứ năm, 6 tháng 3 2025 19:00',
          link: 'https://www.vtix.vn/vi/event/bong-hoa-dep-nhat',
          coupon: 'GROUP012',
          discount: '20%',
          couponDes: 'Giảm lên đến',
        },
        {
          image:
            'https://vtix.vn/event/unicorn/bong-hoa-dep-nhat/banner-mb.webp',
          title: 'Bông Hoa Đẹp Nhất 3',
          date: '20.01.2025',
          hour: '19:00',
          address:
            'Bãi đá sông Hồng, Hà Nội (cuối ngõ 264 Âu Cơ, Quận Tây Hồ, TP. Hà Nội)',
          fullDate: 'Thứ năm, 6 tháng 3 2025 19:00',
          link: 'https://www.vtix.vn/vi/event/bong-hoa-dep-nhat',
          coupon: 'GROUP013',
          discount: '20%',
          couponDes: 'Giảm lên đến',
        },
        {
          image:
            'https://vtix.vn/event/unicorn/bong-hoa-dep-nhat/banner-mb.webp',
          title: 'Bông Hoa Đẹp Nhất 4',
          date: '20.01.2025',
          hour: '19:00',
          address:
            'Bãi đá sông Hồng, Hà Nội (cuối ngõ 264 Âu Cơ, Quận Tây Hồ, TP. Hà Nội)',
          fullDate: 'Thứ năm, 6 tháng 3 2025 19:00',
          link: 'https://www.vtix.vn/vi/event/bong-hoa-dep-nhat',
          coupon: 'GROUP014',
          discount: '20%',
          couponDes: 'Giảm lên đến',
        },
      ],
    };

    const app_domain =
      this.configService.get<string>('APP_DOMAIN') || 'http://localhost';
    const headers = {
      'Content-Type': 'application/json',
      'x-hash-key': this.configService.get<string>('INTERNAL_X_KEY'),
    };

    const getAllUserRole = await firstValueFrom(
      this.httpService.get(`${app_domain}/_api/users/get-all-user-role`, {
        headers,
      }),
    ).then((data) => {
      return data.data;
    });

    if (getAllUserRole.data && !_.isEmpty(getAllUserRole.data)) {
      for (let i = 0; i < getAllUserRole.data.length; i++) {
        const item = getAllUserRole.data[i];
        const email = approve === 'true' ? item.email : '<EMAIL>';
        const data: any = {
          ...defineData,
          fullName: `${item.last_name} ${item.name}`,
        };

        console.info(`<<<<<<<<<<<Begin send to ${email}>>>>>>>>>>>>>`);
        // await this.sendMail({
        //   data,
        //   template: `mail/marketing/${template}`,
        //   email: '<EMAIL>',
        //   subjectEmail: 'Đêm diễn: Bông Hoa Đẹp Nhất dành cho bạn',
        // }).then(async () => {
        //   // Dừng lại sau khi gửi mail thành công
        //   console.info(`<<<<<<<<<<<Sent ${email} finished>>>>>>>>>>>>>`);
        //   await this.delayTime(this.timeDelayNextZone);
        // });
      }
    }

    console.info('<<<<<<<<<<<Send email finished.>>>>>>>>>>>>>');
  }

  @Option({
    flags: '-template, --template <template>',
  })
  parseTemplate(val: string): string {
    return val;
  }

  @Option({
    flags: '-approve, --approve <approve>',
  })
  parseApprove(val: string): string {
    return val;
  }

  async delayTime(time) {
    await new Promise((resolve) => setTimeout(resolve, time));
  }

  // Func gửi email
  async sendMail({ data, template, email, subjectEmail }) {
    try {
      const transporter = nodemailer.createTransport({
        service: config.config_email.service,
        port: config.config_email.port,
        host: config.config_email.host,
        secure: true,
        pool: true,
        auth: {
          user: config.config_email.email_user,
          pass: config.config_email.email_password,
        },
      });
      const templatePath = path.join(
        __dirname,
        '..',
        'views',
        `${template}.ejs`,
      );
      console.log('templatePath > ', templatePath);
      return await ejs.renderFile(
        templatePath,
        data,
        async function (err, html) {
          if (err) {
            console.log('Error rendering template:', err);
            return;
          }
          const mailOptions = {
            from: config.config_email.email_sender,
            to: email,
            subject: `[VTIX] ${subjectEmail}`,
            html: html,
          };
          return await transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
              console.log('SEND MAIL FAIL');
              console.log(err);
            } else {
              console.log('SEND MAIL SUCCESS TO', email);
            }
          });
        },
      );
    } catch (error) {
      console.log('Send email marketing error > ', error);
    }
  }
}
