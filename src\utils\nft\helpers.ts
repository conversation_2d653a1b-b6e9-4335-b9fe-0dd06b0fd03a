import {
  Keypair,
  LAMPORTS_PER_SOL,
  PublicKey,
  PublicKeyInitData,
} from '@solana/web3.js';
import * as fs from 'fs';
import { <PERSON>uffer } from 'buffer';

// define some default locations
// const DEFAULT_KEY_DIR_NAME = ".local_keys";
// const DEFAULT_PUBLIC_KEY_FILE = "keys.json";
// const DEFAULT_DEMO_DATA_FILE = "demo.json";
/*
    Load locally stored PublicKey addresses
  */
export const loadPublicKeysFromFile = (absPath) => {
  try {
    if (!absPath) throw Error('No path provided');
    if (!fs.existsSync(absPath)) throw Error('File does not exist.');

    // load the public keys from the file
    const data =
      JSON.parse(fs.readFileSync(absPath, { encoding: 'utf-8' })) || {};

    // convert all loaded keyed values into valid public keys
    for (const [key, value] of Object.entries(data)) {
      data[key] = new PublicKey(<PublicKeyInitData>value) ?? '';
    }

    return data;
  } catch (err) {
    // console.warn("Unable to load local file");
  }
  // always return an object
  return {};
};

/*
    Locally save demo data to the filesystem for later retrieval
  */
export const saveDemoDataToFile = (name, newData, absPath) => {
  try {
    let data = {};

    // fetch all the current values, when the storage file exists
    if (fs.existsSync(absPath))
      data = JSON.parse(fs.readFileSync(absPath, { encoding: 'utf-8' })) || {};

    data = { ...data, [name]: newData };

    // actually save the data to the file
    fs.writeFileSync(absPath, JSON.stringify(data), {
      encoding: 'utf-8',
    });

    return data;
  } catch (err) {
    console.warn('Unable to save to file');
    // console.warn(err);
  }

  // always return an object
  return {};
};

/*
    Load a locally stored JSON keypair file and convert it to a valid Keypair
  */
export const loadKeypair = async (nft_keypair) => {
  try {
    // parse the loaded secretKey into a valid keypair
    const keypair = await Keypair.fromSecretKey(
      new Uint8Array(Buffer.from(JSON.parse(nft_keypair))),
    );
    return keypair;
  } catch (err) {
    // return false;
    throw err;
  }
};

/*
    Compute the Solana explorer address for the various sets of data
  */
export const explorerURL = ({
  address,
  txSignature,
  cluster,
}: {
  address?: string;
  txSignature?: string;
  cluster?: string;
}) => {
  let baseUrl;
  //
  if (address) baseUrl = `https://explorer.solana.com/address/${address}`;
  else if (txSignature)
    baseUrl = `https://explorer.solana.com/tx/${txSignature}`;
  else return '[unknown]';

  // auto append the desired search params
  const url = new URL(baseUrl);
  url.searchParams.append('cluster', cluster || 'devnet');
  return url.toString() + '\n';
};

/**
 * Auto airdrop the given wallet of of a balance of < 0.5 SOL
 */
export const airdropOnLowBalance = async (
  connection,
  keypair,
  forceAirdrop,
) => {
  // get the current balance
  const balance = await connection.getBalance(keypair.publicKey);

  // define the low balance threshold before airdrop
  const MIN_BALANCE_TO_AIRDROP = LAMPORTS_PER_SOL / 2; // current: 0.5 SOL

  // check the balance of the two accounts, airdrop when low
  if (forceAirdrop === true || balance < MIN_BALANCE_TO_AIRDROP) {
    console.log(
      `Requesting airdrop of 1 SOL to ${keypair.publicKey.toBase58()}...`,
    );
    await connection
      .requestAirdrop(keypair.publicKey, LAMPORTS_PER_SOL)
      .then((sig) => {
        console.log('Tx signature:', sig);
        // balance = balance + LAMPORTS_PER_SOL;
      });

    // fetch the new balance
    // const newBalance = await connection.getBalance(keypair.publicKey);
    // return newBalance;
  }
  // else console.log("Balance of:", balance / LAMPORTS_PER_SOL, "SOL");

  return balance;
};

/*
    Helper function to extract a transaction signature from a failed transaction's error message
  */
export const extractSignatureFromFailedTransaction = async (
  connection,
  err,
  fetchLogs,
) => {
  if (err?.signature) return err.signature;

  // extract the failed transaction's signature
  const failedSig = new RegExp(
    /^((.*)?Error: )?(Transaction|Signature) ([A-Z0-9]{32,}) /gim,
  ).exec(err?.message?.toString())?.[4];

  // ensure a signature was found
  if (failedSig) {
    // when desired, attempt to fetch the program logs from the cluster
    if (fetchLogs)
      await connection
        .getTransaction(failedSig, {
          maxSupportedTransactionVersion: 0,
        })
        .then((tx) => {
          console.log(`\n==== Transaction logs for ${failedSig} ====`);
          console.log(explorerURL({ txSignature: failedSig }), '');
          console.log(
            tx?.meta?.logMessages ?? 'No log messages provided by RPC',
          );
          console.log(`==== END LOGS ====\n`);
        });
    else {
      console.log('\n========================================');
      console.log(explorerURL({ txSignature: failedSig }));
      console.log('========================================\n');
    }
  }

  // always return the failed signature value
  return failedSig;
};

/*
    Standard number formatter
  */
export const numberFormatter = (num, forceDecimals = 2) => {
  // set the significant figures
  // const minimumFractionDigits = num > 1 ? num : forceDecimals;

  // do the formatting
  return new Intl.NumberFormat(undefined, {
    minimumFractionDigits: forceDecimals,
  }).format(num);
};

/*
    Display a separator in the console, with our without a message
  */
export const printConsoleSeparator = (message) => {
  console.log('\n===============================================');
  console.log('===============================================\n');
  if (message) console.log(message);
};
